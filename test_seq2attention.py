#!/usr/bin/env python3
"""
seq2attention模块测试脚本
验证模块的各项功能
"""

import numpy as np
import time
from seq2attention import seq2attention, get_vector_dimension, is_cuda_available, clear_cache


def test_basic_functionality():
    """测试基本功能"""
    print("测试1: 基本功能")
    print("-" * 30)
    
    # 测试序列
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    
    start_time = time.time()
    vector = seq2attention(sequence)
    end_time = time.time()
    
    if vector is not None:
        print(f"[OK] 基本功能正常")
        print(f"  序列长度: {len(sequence)}")
        print(f"  向量维度: {len(vector)}")
        print(f"  向量类型: {type(vector)}")
        print(f"  处理时间: {end_time - start_time:.2f}秒")
        print(f"  向量范围: [{vector.min():.4f}, {vector.max():.4f}]")
        return True
    else:
        print("[FAIL] 基本功能失败")
        return False


def test_batch_processing():
    """测试批量处理"""
    print("\n测试2: 批量处理")
    print("-" * 30)
    
    sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "AKLMNPQRSTUVWXYZAKLMNPQRSTUVWXYZ",
        "DEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ"
    ]
    
    start_time = time.time()
    vectors = seq2attention(sequences)
    end_time = time.time()
    
    success_count = sum(1 for v in vectors if v is not None)
    
    print(f"[OK] 批量处理完成")
    print(f"  输入序列数: {len(sequences)}")
    print(f"  成功处理数: {success_count}")
    print(f"  成功率: {success_count/len(sequences)*100:.1f}%")
    print(f"  总处理时间: {end_time - start_time:.2f}秒")
    print(f"  平均时间: {(end_time - start_time)/len(sequences):.2f}秒/序列")
    
    return success_count > 0


def test_edge_cases():
    """测试边界情况"""
    print("\n测试3: 边界情况")
    print("-" * 30)
    
    test_cases = [
        ("短序列", "MKTVR"),
        ("最小长度", "MKTVRQERLK"),  # 10个残基
        ("空序列", ""),
        ("长序列", "M" * 500),
        ("超长序列", "M" * 1500),
    ]
    
    results = []
    for case_name, sequence in test_cases:
        try:
            vector = seq2attention(sequence)
            if vector is not None:
                status = f"[OK] 成功 (维度: {len(vector)})"
                results.append(True)
            else:
                status = "[SKIP] 返回None (预期行为)"
                results.append(True)  # None也是预期的结果
        except Exception as e:
            status = f"[FAIL] 异常: {str(e)[:50]}..."
            results.append(False)
        
        print(f"  {case_name:10s}: {status}")
    
    success_rate = sum(results) / len(results) * 100
    print(f"边界情况处理成功率: {success_rate:.1f}%")
    
    return success_rate > 80


def test_consistency():
    """测试一致性"""
    print("\n测试4: 结果一致性")
    print("-" * 30)
    
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    
    # 多次提取同一序列的向量
    vectors = []
    for i in range(3):
        vector = seq2attention(sequence)
        if vector is not None:
            vectors.append(vector)
    
    if len(vectors) >= 2:
        # 计算向量间的相似性
        similarities = []
        for i in range(len(vectors)):
            for j in range(i+1, len(vectors)):
                sim = np.dot(vectors[i], vectors[j]) / (np.linalg.norm(vectors[i]) * np.linalg.norm(vectors[j]))
                similarities.append(sim)
        
        avg_similarity = np.mean(similarities)
        print(f"[OK] 一致性测试完成")
        print(f"  测试次数: {len(vectors)}")
        print(f"  平均相似性: {avg_similarity:.6f}")
        
        if avg_similarity > 0.999:
            print(f"  结论: 结果高度一致")
            return True
        else:
            print(f"  结论: 结果存在差异")
            return False
    else:
        print("[FAIL] 一致性测试失败 - 无法获得足够的向量")
        return False


def test_performance():
    """测试性能"""
    print("\n测试5: 性能测试")
    print("-" * 30)
    
    # 不同长度的序列
    test_sequences = [
        ("短序列 (50)", "M" * 50),
        ("中等序列 (200)", "M" * 200),
        ("长序列 (500)", "M" * 500),
        ("最长序列 (1000)", "M" * 1000),
    ]
    
    for seq_name, sequence in test_sequences:
        start_time = time.time()
        vector = seq2attention(sequence)
        end_time = time.time()
        
        if vector is not None:
            print(f"  {seq_name:15s}: {end_time - start_time:.2f}秒")
        else:
            print(f"  {seq_name:15s}: 处理失败")
    
    return True


def test_utility_functions():
    """测试工具函数"""
    print("\n测试6: 工具函数")
    print("-" * 30)
    
    # 测试维度函数
    dim = get_vector_dimension()
    print(f"  向量维度: {dim}")
    
    # 测试CUDA检查
    cuda_available = is_cuda_available()
    print(f"  CUDA可用: {cuda_available}")
    
    # 测试缓存清理
    try:
        clear_cache()
        print(f"  缓存清理: [OK] 成功")
        cache_ok = True
    except Exception as e:
        print(f"  缓存清理: [FAIL] 失败 ({e})")
        cache_ok = False
    
    return dim == 1280 and cache_ok


def main():
    """主测试函数"""
    print("="*50)
    print("seq2attention模块功能测试")
    print("="*50)
    
    # 显示系统信息
    print(f"CUDA可用: {is_cuda_available()}")
    print(f"向量维度: {get_vector_dimension()}")
    print()
    
    # 运行所有测试
    tests = [
        test_basic_functionality,
        test_batch_processing,
        test_edge_cases,
        test_consistency,
        test_performance,
        test_utility_functions
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试异常: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总")
    print("="*50)
    
    test_names = [
        "基本功能",
        "批量处理", 
        "边界情况",
        "结果一致性",
        "性能测试",
        "工具函数"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "[OK] 通过" if result else "[FAIL] 失败"
        print(f"{i+1}. {name:12s}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("[SUCCESS] 所有测试通过！模块功能正常")
    elif passed >= len(tests) - 1:
        print("[WARNING] 大部分测试通过，模块基本可用")
    else:
        print("[ERROR] 多项测试失败，请检查模块配置")
    
    # 清理缓存
    clear_cache()
    print("\n[OK] 测试完成，缓存已清理")


if __name__ == "__main__":
    main()
