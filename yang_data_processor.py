#!/usr/bin/env python3
"""
Yang_Data.xlsx专用处理器
提取id、name、Sequence三列，将Sequence转换为architectural融合特征向量
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
import gc

from transformers import EsmModel, EsmTokenizer
import esm  # Facebook ESM库，用于MSA模型

# 设置离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yang_data_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ESM_MSA_FusionModel(nn.Module):
    """ESM-2 + MSA特征融合模型"""
    
    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):
        super().__init__()
        
        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.fusion_dim = fusion_dim
        
        # ESM-2特征投影
        self.esm_projection = nn.Sequential(
            nn.Linear(esm_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # MSA特征投影
        self.msa_projection = nn.Sequential(
            nn.Linear(msa_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 注意力融合机制
        self.attention_fusion = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
    def forward(self, esm_features: torch.Tensor, msa_features: torch.Tensor) -> torch.Tensor:
        # 投影到相同维度
        esm_proj = self.esm_projection(esm_features)
        msa_proj = self.msa_projection(msa_features)
        
        # 注意力融合
        attended_features, _ = self.attention_fusion(esm_proj, msa_proj, msa_proj)
        attended_features = attended_features + esm_proj
        
        # 拼接和最终融合
        concatenated = torch.cat([esm_proj, attended_features], dim=-1)
        fused_features = self.final_fusion(concatenated)
        
        return fused_features


class YangDataProcessor:
    """Yang_Data.xlsx专用处理器"""
    
    def __init__(self, max_sequence_length: int = 1024):
        """
        初始化处理器
        
        Args:
            max_sequence_length: 最大序列长度
        """
        self.max_sequence_length = max_sequence_length
        
        # 初始化模型
        self._initialize_models()
        
    def _get_local_esm_path(self):
        """获取本地ESM-2模型路径"""
        base_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D"
        snapshots_path = os.path.join(base_path, "snapshots")
        if os.path.exists(snapshots_path):
            snapshot_dirs = [d for d in os.listdir(snapshots_path) 
                           if os.path.isdir(os.path.join(snapshots_path, d))]
            if snapshot_dirs:
                model_path = os.path.join(snapshots_path, snapshot_dirs[0])
                required_files = ['config.json', 'pytorch_model.bin', 'tokenizer_config.json']
                if all(os.path.exists(os.path.join(model_path, f)) for f in required_files):
                    return model_path
        return None
        
    def _initialize_models(self):
        """初始化ESM-2和MSA模型"""
        logger.info("初始化ESM-2和MSA模型...")
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"使用设备: {self.device}")
        
        try:
            # 1. 加载ESM-2模型
            logger.info("加载ESM-2模型...")
            local_esm_path = self._get_local_esm_path()
            if local_esm_path:
                logger.info(f"使用本地ESM-2模型: {local_esm_path}")
                self.esm_tokenizer = EsmTokenizer.from_pretrained(local_esm_path, local_files_only=True)
                self.esm_model = EsmModel.from_pretrained(local_esm_path, local_files_only=True)
            else:
                logger.info("使用在线ESM-2模型")
                model_name = "facebook/esm2_t33_650M_UR50D"
                self.esm_tokenizer = EsmTokenizer.from_pretrained(model_name)
                self.esm_model = EsmModel.from_pretrained(model_name)
            
            self.esm_model.to(self.device)
            self.esm_model.eval()
            logger.info("[OK] ESM-2模型加载成功")
            
            # 2. 加载MSA模型
            logger.info("加载MSA模型...")
            torch.hub.set_dir('/home/<USER>/.cache/torch/hub')
            self.msa_model, self.msa_alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()
            self.msa_model.to(self.device)
            self.msa_model.eval()
            logger.info("[OK] MSA模型加载成功")
            
            # 3. 初始化融合模型
            logger.info("初始化融合模型...")
            esm_dim = self.esm_model.config.hidden_size
            msa_dim = self.msa_model.args.embed_dim
            
            self.fusion_model = ESM_MSA_FusionModel(
                esm_dim=esm_dim,
                msa_dim=msa_dim,
                fusion_dim=1280
            )
            self.fusion_model.to(self.device)
            self.fusion_model.eval()
            logger.info("[OK] 融合模型初始化成功")
            
            # 打印模型信息
            esm_params = sum(p.numel() for p in self.esm_model.parameters())
            msa_params = sum(p.numel() for p in self.msa_model.parameters())
            fusion_params = sum(p.numel() for p in self.fusion_model.parameters())
            
            logger.info(f"ESM-2模型参数量: {esm_params:,}")
            logger.info(f"MSA模型参数量: {msa_params:,}")
            logger.info(f"融合模型参数量: {fusion_params:,}")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise
    
    def _create_msa_from_sequence(self, sequence: str, num_sequences: int = 64) -> List[str]:
        """从单个序列创建MSA（简化版本）"""
        msa_sequences = [sequence]  # 原序列作为第一条
        
        # 为了演示，添加一些轻微变异的序列
        for i in range(min(num_sequences - 1, 31)):
            msa_sequences.append(sequence)
        
        return msa_sequences
    
    def extract_esm_features(self, sequence: str) -> torch.Tensor:
        """提取ESM-2特征"""
        inputs = self.esm_tokenizer(
            sequence, 
            return_tensors="pt", 
            padding=True, 
            truncation=True,
            max_length=self.max_sequence_length + 2
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.esm_model(**inputs)
        
        features = outputs.last_hidden_state[0, 1:-1, :]
        return features
    
    def extract_msa_features(self, msa_sequences: List[str]) -> torch.Tensor:
        """提取MSA特征"""
        msa_data = []
        for seq in msa_sequences:
            if len(seq) > self.max_sequence_length:
                seq = seq[:self.max_sequence_length]
            msa_data.append(("", seq))
        
        msa_batch_converter = self.msa_alphabet.get_batch_converter()
        msa_batch_labels, msa_batch_strs, msa_batch_tokens = msa_batch_converter([msa_data])
        
        msa_batch_tokens = msa_batch_tokens.to(self.device)
        
        with torch.no_grad():
            results = self.msa_model(msa_batch_tokens, repr_layers=[12])
        
        msa_features = results["representations"][12][0, 0, 1:-1, :]
        return msa_features
    
    def extract_architectural_fusion_vector(self, sequence: str, sample_id: str) -> Optional[np.ndarray]:
        """提取architectural融合特征向量"""
        try:
            # 检查序列长度
            if len(sequence) > self.max_sequence_length:
                logger.warning(f"序列 {sample_id} 过长 ({len(sequence)} 残基)，截断到 {self.max_sequence_length}")
                sequence = sequence[:self.max_sequence_length]
            
            if len(sequence) < 10:
                logger.warning(f"序列 {sample_id} 过短 ({len(sequence)} 残基)，跳过处理")
                return None
            
            # 1. 提取ESM-2特征
            esm_features = self.extract_esm_features(sequence)
            
            # 2. 创建MSA并提取MSA特征
            msa_sequences = self._create_msa_from_sequence(sequence)
            msa_features = self.extract_msa_features(msa_sequences)
            
            # 3. 确保特征长度一致
            min_len = min(esm_features.size(0), msa_features.size(0))
            esm_features = esm_features[:min_len, :]
            msa_features = msa_features[:min_len, :]
            
            # 4. 添加batch维度
            esm_features = esm_features.unsqueeze(0)
            msa_features = msa_features.unsqueeze(0)
            
            # 5. architectural特征融合
            with torch.no_grad():
                fused_features = self.fusion_model(esm_features, msa_features)
            
            # 6. 序列级池化（平均）
            pooled_features = fused_features.mean(dim=1).squeeze(0)
            
            # 7. 转换为numpy
            vector = pooled_features.cpu().numpy()
            
            logger.debug(f"成功提取 {sample_id} 的architectural融合向量，维度: {vector.shape}")
            
            return vector
            
        except Exception as e:
            logger.error(f"提取 {sample_id} architectural融合向量失败: {e}")
            return None
        finally:
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()

    def load_yang_data(self, excel_file: str) -> pd.DataFrame:
        """加载Yang_Data.xlsx文件"""
        logger.info(f"加载Yang数据文件: {excel_file}")

        try:
            df = pd.read_excel(excel_file, engine='openpyxl')
            logger.info(f"原始数据形状: {df.shape}")
            logger.info(f"列名: {df.columns.tolist()}")

            # 提取需要的列：id, name, Sequence
            required_columns = ['id', 'name', 'Sequence']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必需的列: {missing_columns}")

            # 提取需要的列
            data = df[required_columns].copy()

            # 清理数据
            data = data.dropna(subset=['id', 'name', 'Sequence'])
            data = data[data['Sequence'].str.len() > 0]

            # 确保id列为字符串类型
            data['id'] = data['id'].astype(str)

            logger.info(f"清理后数据形状: {data.shape}")
            logger.info(f"样本预览:")
            for i, row in data.head(3).iterrows():
                seq_len = len(row['Sequence']) if pd.notna(row['Sequence']) else 0
                logger.info(f"  {row['id']}: {row['name']}, 序列长度={seq_len}")

            return data

        except Exception as e:
            logger.error(f"加载Yang数据失败: {e}")
            raise

    def process_yang_data(self, input_file: str, output_file: str = "architectural_fusion.csv"):
        """处理Yang_Data.xlsx并生成architectural_fusion.csv"""
        logger.info("开始处理Yang数据...")

        # 加载数据
        data = self.load_yang_data(input_file)

        # 准备结果列表
        results = []

        # 逐个处理每个样本
        for idx, row in tqdm(data.iterrows(), total=len(data), desc="提取architectural融合特征"):
            sample_id = str(row['id'])
            name = row['name']
            sequence = row['Sequence']

            logger.info(f"处理样本 {sample_id}: {name} (序列长度: {len(sequence)})")

            # 提取architectural融合向量
            vector = self.extract_architectural_fusion_vector(sequence, sample_id)

            if vector is not None:
                # 将向量转换为逗号分隔的字符串
                vector_str = ','.join(map(str, vector))

                result_row = {
                    'id': sample_id,
                    'name': name,
                    'Sequence': sequence,
                    'vector': vector_str
                }

                logger.info(f"[OK] {sample_id} ({name}) 处理成功，向量维度: {len(vector)}")

            else:
                # 处理失败，记录空向量
                result_row = {
                    'id': sample_id,
                    'name': name,
                    'Sequence': sequence,
                    'vector': None
                }

                logger.warning(f"[FAIL] {sample_id} ({name}) 处理失败")

            results.append(result_row)

        # 转换为DataFrame并保存
        results_df = pd.DataFrame(results)
        results_df.to_csv(output_file, index=False)

        logger.info(f"处理完成！结果保存到: {output_file}")

        # 显示统计信息
        successful_count = results_df['vector'].notna().sum()
        failed_count = results_df['vector'].isna().sum()

        logger.info(f"统计信息:")
        logger.info(f"  总样本数: {len(results_df)}")
        logger.info(f"  成功处理: {successful_count}")
        logger.info(f"  处理失败: {failed_count}")
        logger.info(f"  成功率: {successful_count / len(results_df) * 100:.1f}%")

        if successful_count > 0:
            # 检查向量维度
            first_successful = results_df[results_df['vector'].notna()].iloc[0]
            vector_dim = len(first_successful['vector'].split(','))
            logger.info(f"  向量维度: {vector_dim}")

        return results_df


def main():
    """主函数"""
    print("="*80)
    print("Yang_Data.xlsx Architectural融合特征提取器")
    print("提取id、name、Sequence，将Sequence转换为architectural融合向量")
    print("="*80)

    # 检查输入文件
    input_file = "Yang_Data.xlsx"
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return

    # 输出文件
    output_file = "architectural_fusion.csv"

    try:
        # 初始化处理器
        print("\n初始化architectural融合处理器...")
        processor = YangDataProcessor(max_sequence_length=1024)

        # 预览数据
        print(f"\n预览输入数据...")
        data = processor.load_yang_data(input_file)

        print(f"\n数据概览:")
        print(f"总样本数: {len(data)}")

        # 序列长度统计
        seq_lengths = data['Sequence'].str.len()
        print(f"序列长度统计:")
        print(f"  最小: {seq_lengths.min()}")
        print(f"  最大: {seq_lengths.max()}")
        print(f"  平均: {seq_lengths.mean():.1f}")

        # 显示所有样本
        print(f"\n所有样本:")
        for i, row in data.iterrows():
            seq_len = len(row['Sequence'])
            status = "可处理" if 10 <= seq_len <= 1024 else ("截断" if seq_len > 1024 else "跳过")
            print(f"  {row['id']}: {row['name']}, 长度={seq_len}, 状态={status}")

        # 询问是否继续
        print(f"\n准备处理 {len(data)} 个样本")
        print("特点:")
        print("  [OK] 使用architectural融合方法 (ESM-2 + MSA)")
        print("  [OK] 提取id、name、Sequence三列")
        print("  [OK] 将Sequence转换为1280维融合向量")
        print(f"  [OK] 保存为 {output_file}")

        response = input("\n是否开始处理? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("处理已取消")
            return

        # 开始处理
        print(f"\n开始处理...")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 处理数据
        results = processor.process_yang_data(input_file, output_file)

        # 显示最终结果
        print(f"\n处理完成!")
        print(f"输出文件: {output_file}")

        # 显示结果预览
        if os.path.exists(output_file):
            print(f"\n结果文件预览:")
            result_df = pd.read_csv(output_file)
            print(f"文件大小: {os.path.getsize(output_file) / 1024:.2f} KB")
            print(f"包含列: {result_df.columns.tolist()}")

            # 显示前几行
            print(f"\n前3行数据:")
            for i, row in result_df.head(3).iterrows():
                vector_preview = str(row['vector'])[:50] + "..." if pd.notna(row['vector']) and len(str(row['vector'])) > 50 else str(row['vector'])
                print(f"  {row['id']}: {row['name']}, vector={vector_preview}")

        print(f"\n处理完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)

    except KeyboardInterrupt:
        print(f"\n\n处理被用户中断")

    except Exception as e:
        print(f"\n处理过程中出现错误: {e}")
        logger.error(f"处理错误: {e}", exc_info=True)


if __name__ == "__main__":
    main()
