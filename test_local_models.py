#!/usr/bin/env python3
"""
测试本地模型加载和基本特征提取
"""

import os
import torch
import pandas as pd
import numpy as np
from transformers import EsmModel, EsmTokenizer
import logging

# 设置环境变量，优先使用本地缓存
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_local_esm_model():
    """测试本地ESM-2模型加载"""
    print("1. 测试本地ESM-2模型加载...")
    
    model_name = "facebook/esm2_t33_650M_UR50D"
    cache_dir = "/home/<USER>/.cache/huggingface"
    
    try:
        print(f"   模型名称: {model_name}")
        print(f"   缓存目录: {cache_dir}")
        
        # 检查缓存目录
        model_cache_path = os.path.join(cache_dir, "hub", f"models--facebook--esm2_t33_650M_UR50D")
        if os.path.exists(model_cache_path):
            print(f"   ✓ 找到模型缓存: {model_cache_path}")
        else:
            print(f"   ✗ 未找到模型缓存: {model_cache_path}")
            return None, None, None
        
        # 加载tokenizer（本地优先）
        try:
            tokenizer = EsmTokenizer.from_pretrained(
                model_name, 
                local_files_only=True,
                cache_dir=cache_dir
            )
            print("   ✓ Tokenizer从本地缓存加载成功")
        except Exception as e:
            print(f"   ⚠ 本地tokenizer加载失败，尝试在线: {e}")
            tokenizer = EsmTokenizer.from_pretrained(model_name)
            print("   ✓ Tokenizer在线加载成功")
        
        # 加载模型（本地优先）
        try:
            model = EsmModel.from_pretrained(
                model_name,
                local_files_only=True,
                cache_dir=cache_dir
            )
            print("   ✓ 模型从本地缓存加载成功")
        except Exception as e:
            print(f"   ⚠ 本地模型加载失败，尝试在线: {e}")
            model = EsmModel.from_pretrained(model_name)
            print("   ✓ 模型在线加载成功")
        
        # 设置设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        model.eval()
        print(f"   ✓ 模型移动到设备: {device}")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数量: {total_params:,}")
        
        return tokenizer, model, device
        
    except Exception as e:
        print(f"   ✗ 模型加载失败: {e}")
        return None, None, None

def test_simple_feature_extraction(tokenizer, model, device):
    """测试简单的特征提取"""
    print("\n2. 测试简单特征提取...")
    
    if tokenizer is None or model is None:
        print("   ✗ 模型未加载，跳过测试")
        return False
    
    try:
        # 使用一个短序列进行测试
        test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        print(f"   测试序列: {test_sequence[:30]}...")
        print(f"   序列长度: {len(test_sequence)}")
        
        # Tokenize
        inputs = tokenizer(test_sequence, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        print(f"   ✓ Tokenization成功，输入形状: {inputs['input_ids'].shape}")
        
        # 前向传播
        with torch.no_grad():
            outputs = model(**inputs)
        
        print("   ✓ 前向传播成功")
        
        # 提取特征
        last_hidden_state = outputs.last_hidden_state
        print(f"   输出形状: {last_hidden_state.shape}")
        
        # 去除特殊token（CLS和SEP）
        sequence_features = last_hidden_state[0, 1:-1, :]  # (seq_len, hidden_dim)
        print(f"   序列特征形状: {sequence_features.shape}")
        
        # 序列级池化
        pooled_features = sequence_features.mean(dim=0)  # (hidden_dim,)
        print(f"   池化特征形状: {pooled_features.shape}")
        
        # 转换为numpy
        feature_vector = pooled_features.cpu().numpy()
        print(f"   特征向量维度: {feature_vector.shape[0]}")
        print(f"   特征统计: 均值={feature_vector.mean():.4f}, 标准差={feature_vector.std():.4f}")
        print(f"   特征范围: [{feature_vector.min():.4f}, {feature_vector.max():.4f}]")
        
        return feature_vector
        
    except Exception as e:
        print(f"   ✗ 特征提取失败: {e}")
        return None

def test_data_sample():
    """测试处理数据样本"""
    print("\n3. 测试数据样本处理...")
    
    try:
        # 加载数据
        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')
        print(f"   ✓ 数据加载成功: {df.shape}")
        
        # 取前3个样本
        sample_data = df[['ID', 'Sequence']].head(3)
        print(f"   测试样本数: {len(sample_data)}")
        
        for i, row in sample_data.iterrows():
            protein_id = row['ID']
            sequence = row['Sequence']
            
            if pd.isna(sequence):
                print(f"   样本 {i+1}: {protein_id} - 序列为空，跳过")
                continue
                
            seq_len = len(sequence)
            print(f"   样本 {i+1}: {protein_id} - 序列长度: {seq_len}")
            
            # 检查序列是否过长
            if seq_len > 1000:
                print(f"     ⚠ 序列过长，实际处理时会跳过")
            else:
                print(f"     ✓ 序列长度适中，可以处理")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 数据样本测试失败: {e}")
        return False

def test_memory_usage():
    """测试内存使用情况"""
    print("\n4. 测试内存使用...")
    
    try:
        if torch.cuda.is_available():
            # GPU内存
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            gpu_allocated = torch.cuda.memory_allocated() / 1024**3
            gpu_cached = torch.cuda.memory_reserved() / 1024**3
            
            print(f"   GPU总内存: {gpu_memory:.2f} GB")
            print(f"   GPU已分配: {gpu_allocated:.2f} GB")
            print(f"   GPU缓存: {gpu_cached:.2f} GB")
            print(f"   GPU可用: {gpu_memory - gpu_cached:.2f} GB")
            
            if gpu_memory - gpu_cached > 4:
                print("   ✓ GPU内存充足")
            else:
                print("   ⚠ GPU内存可能不足，建议处理较短序列")
        else:
            print("   使用CPU模式")
        
        return True
        
    except Exception as e:
        print(f"   ⚠ 内存检查失败: {e}")
        return True

def main():
    """主测试函数"""
    print("="*60)
    print("本地模型加载和基础功能测试")
    print("="*60)
    
    # 测试本地模型加载
    tokenizer, model, device = test_local_esm_model()
    
    if tokenizer is None or model is None:
        print("\n✗ 模型加载失败，无法继续测试")
        return
    
    # 测试特征提取
    feature_vector = test_simple_feature_extraction(tokenizer, model, device)
    
    # 测试数据样本
    data_ok = test_data_sample()
    
    # 测试内存使用
    memory_ok = test_memory_usage()
    
    # 汇总结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    model_ok = tokenizer is not None and model is not None
    feature_ok = feature_vector is not None
    
    tests = [
        ("本地模型加载", model_ok),
        ("特征提取", feature_ok),
        ("数据样本处理", data_ok),
        ("内存检查", memory_ok)
    ]
    
    passed = 0
    for i, (name, result) in enumerate(tests):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("✓ 所有测试通过，可以开始正式处理！")
        print("运行命令: python run_fusion.py")
    elif passed >= 3:
        print("⚠ 主要功能正常，可以尝试运行")
        print("运行命令: python run_fusion.py")
    else:
        print("✗ 关键功能测试失败，请检查环境")
    
    print("="*60)

if __name__ == "__main__":
    main()
