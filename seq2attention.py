#!/usr/bin/env python3
"""
seq2attention.py - Architectural融合特征提取模块

提供seq2attention函数，将蛋白质序列转换为architectural融合向量
基于ESM-2 + MSA Transformer的架构级融合方法

使用示例:
    from seq2attention import seq2attention
    
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    vector = seq2attention(sequence)
    print(f"融合向量维度: {len(vector)}")
"""

import numpy as np
import torch
import torch.nn as nn
import os
import logging
from typing import List, Optional, Union
import warnings
import gc

from transformers import EsmModel, EsmTokenizer
import esm

# 设置离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

# 抑制警告
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.WARNING)  # 只显示警告和错误
logger = logging.getLogger(__name__)


class ESM_MSA_FusionModel(nn.Module):
    """ESM-2 + MSA特征融合模型"""
    
    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):
        super().__init__()
        
        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.fusion_dim = fusion_dim
        
        # ESM-2特征投影
        self.esm_projection = nn.Sequential(
            nn.Linear(esm_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # MSA特征投影
        self.msa_projection = nn.Sequential(
            nn.Linear(msa_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 注意力融合机制
        self.attention_fusion = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
    def forward(self, esm_features: torch.Tensor, msa_features: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 投影到相同维度
        esm_proj = self.esm_projection(esm_features)
        msa_proj = self.msa_projection(msa_features)
        
        # 注意力融合
        attended_features, _ = self.attention_fusion(esm_proj, msa_proj, msa_proj)
        attended_features = attended_features + esm_proj
        
        # 拼接和最终融合
        concatenated = torch.cat([esm_proj, attended_features], dim=-1)
        fused_features = self.final_fusion(concatenated)
        
        return fused_features


class ArchitecturalFusionExtractor:
    """Architectural融合特征提取器"""
    
    def __init__(self, max_sequence_length: int = 1024, device: Optional[str] = None):
        """
        初始化提取器
        
        Args:
            max_sequence_length: 最大序列长度
            device: 计算设备 ('cuda', 'cpu', 或 None 自动选择)
        """
        self.max_sequence_length = max_sequence_length
        self.device = torch.device(device if device else ("cuda" if torch.cuda.is_available() else "cpu"))
        
        # 模型初始化标志
        self._models_initialized = False
        
        # 延迟初始化模型（在第一次调用时初始化）
        self.esm_tokenizer = None
        self.esm_model = None
        self.msa_model = None
        self.msa_alphabet = None
        self.fusion_model = None
    
    def _get_local_esm_path(self):
        """获取本地ESM-2模型路径"""
        base_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D"
        snapshots_path = os.path.join(base_path, "snapshots")
        if os.path.exists(snapshots_path):
            snapshot_dirs = [d for d in os.listdir(snapshots_path) 
                           if os.path.isdir(os.path.join(snapshots_path, d))]
            if snapshot_dirs:
                model_path = os.path.join(snapshots_path, snapshot_dirs[0])
                required_files = ['config.json', 'pytorch_model.bin', 'tokenizer_config.json']
                if all(os.path.exists(os.path.join(model_path, f)) for f in required_files):
                    return model_path
        return None
    
    def _initialize_models(self):
        """初始化模型（延迟加载）"""
        if self._models_initialized:
            return
        
        try:
            # 1. 加载ESM-2模型
            local_esm_path = self._get_local_esm_path()
            if local_esm_path:
                self.esm_tokenizer = EsmTokenizer.from_pretrained(local_esm_path, local_files_only=True)
                self.esm_model = EsmModel.from_pretrained(local_esm_path, local_files_only=True)
            else:
                model_name = "facebook/esm2_t33_650M_UR50D"
                self.esm_tokenizer = EsmTokenizer.from_pretrained(model_name)
                self.esm_model = EsmModel.from_pretrained(model_name)
            
            self.esm_model.to(self.device)
            self.esm_model.eval()
            
            # 2. 加载MSA模型
            torch.hub.set_dir('/home/<USER>/.cache/torch/hub')
            self.msa_model, self.msa_alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()
            self.msa_model.to(self.device)
            self.msa_model.eval()
            
            # 3. 初始化融合模型
            esm_dim = self.esm_model.config.hidden_size
            msa_dim = self.msa_model.args.embed_dim
            
            self.fusion_model = ESM_MSA_FusionModel(
                esm_dim=esm_dim,
                msa_dim=msa_dim,
                fusion_dim=1280
            )
            self.fusion_model.to(self.device)
            self.fusion_model.eval()
            
            self._models_initialized = True
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise RuntimeError(f"无法初始化architectural融合模型: {e}")
    
    def _create_msa_from_sequence(self, sequence: str, num_sequences: int = 64) -> List[str]:
        """从单个序列创建MSA"""
        msa_sequences = [sequence]
        
        # 添加重复序列（实际应用中应该使用真正的同源序列）
        for i in range(min(num_sequences - 1, 31)):
            msa_sequences.append(sequence)
        
        return msa_sequences
    
    def _extract_esm_features(self, sequence: str) -> torch.Tensor:
        """提取ESM-2特征"""
        inputs = self.esm_tokenizer(
            sequence, 
            return_tensors="pt", 
            padding=True, 
            truncation=True,
            max_length=self.max_sequence_length + 2
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.esm_model(**inputs)
        
        features = outputs.last_hidden_state[0, 1:-1, :]
        return features
    
    def _extract_msa_features(self, msa_sequences: List[str]) -> torch.Tensor:
        """提取MSA特征"""
        msa_data = []
        for seq in msa_sequences:
            if len(seq) > self.max_sequence_length:
                seq = seq[:self.max_sequence_length]
            msa_data.append(("", seq))
        
        msa_batch_converter = self.msa_alphabet.get_batch_converter()
        msa_batch_labels, msa_batch_strs, msa_batch_tokens = msa_batch_converter([msa_data])
        
        msa_batch_tokens = msa_batch_tokens.to(self.device)
        
        with torch.no_grad():
            results = self.msa_model(msa_batch_tokens, repr_layers=[12])
        
        msa_features = results["representations"][12][0, 0, 1:-1, :]
        return msa_features
    
    def extract_fusion_vector(self, sequence: str) -> Optional[np.ndarray]:
        """
        提取architectural融合向量
        
        Args:
            sequence: 蛋白质序列
            
        Returns:
            1280维融合向量，如果失败返回None
        """
        try:
            # 延迟初始化模型
            self._initialize_models()
            
            # 检查序列长度
            if len(sequence) > self.max_sequence_length:
                sequence = sequence[:self.max_sequence_length]
            
            if len(sequence) < 10:
                logger.warning(f"序列过短 ({len(sequence)} 残基)，跳过处理")
                return None
            
            # 1. 提取ESM-2特征
            esm_features = self._extract_esm_features(sequence)
            
            # 2. 创建MSA并提取MSA特征
            msa_sequences = self._create_msa_from_sequence(sequence)
            msa_features = self._extract_msa_features(msa_sequences)
            
            # 3. 确保特征长度一致
            min_len = min(esm_features.size(0), msa_features.size(0))
            esm_features = esm_features[:min_len, :]
            msa_features = msa_features[:min_len, :]
            
            # 4. 添加batch维度
            esm_features = esm_features.unsqueeze(0)
            msa_features = msa_features.unsqueeze(0)
            
            # 5. architectural特征融合
            with torch.no_grad():
                fused_features = self.fusion_model(esm_features, msa_features)
            
            # 6. 序列级池化（平均）
            pooled_features = fused_features.mean(dim=1).squeeze(0)
            
            # 7. 转换为numpy
            vector = pooled_features.cpu().numpy()
            
            return vector
            
        except Exception as e:
            logger.error(f"提取融合向量失败: {e}")
            return None
        finally:
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()


# 全局提取器实例（单例模式）
_global_extractor = None


def seq2attention(sequence: Union[str, List[str]], 
                 max_length: int = 1024,
                 device: Optional[str] = None) -> Union[np.ndarray, List[np.ndarray], None]:
    """
    将蛋白质序列转换为architectural融合向量
    
    Args:
        sequence: 蛋白质序列字符串，或序列列表
        max_length: 最大序列长度，默认1024
        device: 计算设备 ('cuda', 'cpu', 或 None 自动选择)
        
    Returns:
        - 单个序列: 1280维numpy数组，失败返回None
        - 序列列表: numpy数组列表，失败的序列对应None
        
    Examples:
        # 单个序列
        vector = seq2attention("MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG")
        
        # 多个序列
        sequences = ["MKTVRQ...", "AKLMNT..."]
        vectors = seq2attention(sequences)
    """
    global _global_extractor
    
    # 初始化全局提取器（单例模式）
    if _global_extractor is None:
        _global_extractor = ArchitecturalFusionExtractor(
            max_sequence_length=max_length,
            device=device
        )
    
    # 处理单个序列
    if isinstance(sequence, str):
        return _global_extractor.extract_fusion_vector(sequence)
    
    # 处理序列列表
    elif isinstance(sequence, (list, tuple)):
        results = []
        for seq in sequence:
            if isinstance(seq, str):
                vector = _global_extractor.extract_fusion_vector(seq)
                results.append(vector)
            else:
                logger.warning(f"跳过非字符串序列: {type(seq)}")
                results.append(None)
        return results
    
    else:
        logger.error(f"不支持的序列类型: {type(sequence)}")
        return None


# 便捷函数
def get_vector_dimension() -> int:
    """获取融合向量的维度"""
    return 1280


def is_cuda_available() -> bool:
    """检查CUDA是否可用"""
    return torch.cuda.is_available()


def clear_cache():
    """清理模型缓存"""
    global _global_extractor
    if _global_extractor is not None:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()


if __name__ == "__main__":
    # 测试代码
    print("seq2attention模块测试")
    print("="*50)
    
    # 测试序列
    test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    
    print(f"测试序列: {test_sequence[:30]}...")
    print(f"序列长度: {len(test_sequence)}")
    print(f"CUDA可用: {is_cuda_available()}")
    print(f"向量维度: {get_vector_dimension()}")
    
    print("\n开始提取融合向量...")
    vector = seq2attention(test_sequence)
    
    if vector is not None:
        print("[OK] 提取成功!")
        print(f"向量维度: {len(vector)}")
        print(f"向量范围: [{vector.min():.4f}, {vector.max():.4f}]")
        print(f"向量均值: {vector.mean():.4f}")
        print(f"向量标准差: {vector.std():.4f}")
        print(f"向量预览: {vector[:5]}...")
    else:
        print("[FAIL] 提取失败")
    
    print("\n测试完成!")
