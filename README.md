# 架构级融合蛋白质特征提取方法

## 概述

本模块实现了参考Evoformer架构的双流网络架构级融合方法，设计了两个平行的网络分支分别处理ESM-2特征和MSA特征，并在多个中间层进行信息交换。

## 特点

- **双流架构**: 设计两个平行的网络分支，分别处理单序列流和进化流
- **多层交互**: 在多个中间层进行信息交换，充分利用不同特征的特性
- **Evoformer启发**: 参考AlphaFold2的Evoformer设计哲学

## 文件结构

```
architectural_fusion/
├── README.md                 # 使用说明
├── requirements.txt          # 依赖包
├── feature_extractor.py      # 特征提取器
├── evoformer_model.py        # Evoformer风格模型
├── utils.py                  # 工具函数
└── example.py               # 使用示例
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

```python
from evoformer_model import EvoformerModel
from feature_extractor import ProteinFeatureExtractor

# 初始化特征提取器和模型
extractor = ProteinFeatureExtractor()
model = EvoformerModel(esm_dim=1280, msa_dim=256, pair_dim=128, num_blocks=4)

# 提取特征并融合
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
msa_repr, pair_repr = model.extract_and_fuse(sequence, extractor)
```

## 详细使用说明

### 1. 环境配置

确保您的系统满足以下要求：
- Python 3.8+
- CUDA 11.0+ (强烈推荐，架构复杂度高)
- 至少32GB内存 (Evoformer计算量很大)
- 推荐使用高端GPU (V100, A100等)

### 2. 安装步骤

```bash
# 进入目录
cd architectural_fusion

# 安装依赖
pip install -r requirements.txt

# 安装额外依赖
pip install einops torch-geometric

# 可选：安装HHblits用于高质量MSA生成
# 推荐使用较大的数据库如UniRef30
```

### 3. 基本使用

#### 3.1 简单示例

```python
from feature_extractor import ProteinFeatureExtractor
from evoformer_model import EvoformerModel

# 初始化特征提取器
extractor = ProteinFeatureExtractor(esm_model_name="facebook/esm2_t33_650M_UR50D")

# 初始化Evoformer模型
model = EvoformerModel(
    esm_dim=1280,      # ESM-2特征维度
    msa_dim=256,       # MSA特征维度
    pair_dim=128,      # Pair特征维度
    num_blocks=4,      # Evoformer块数量
    num_heads=8,       # 注意力头数
    dropout=0.1
)

# 处理序列
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
results = model.extract_and_fuse(sequence, extractor)

# 获取三种核心表示
msa_repr = results["msa_representation"]      # MSA表示
pair_repr = results["pair_representation"]    # Pair表示
single_repr = results["single_representation"] # Single表示
fused_features = results["fused_features"]    # 最终融合特征
```

#### 3.2 结构预测

```python
# 进行结构预测
structure_predictions = model.get_structure_predictions(results["pair_representation"])

contact_probs = structure_predictions["contact_probabilities"]
print(f"接触图形状: {contact_probs.shape}")
print(f"高置信度接触数 (>0.8): {(contact_probs > 0.8).sum().item()}")

# 可视化接触图
from utils import visualize_contact_map
visualize_contact_map(contact_probs[0], sequence, save_path="contact_map.png")
```

#### 3.3 表示分析

```python
from utils import analyze_evoformer_outputs, print_evoformer_summary

# 打印详细摘要
print_evoformer_summary(results)

# 分析各种表示
analysis = analyze_evoformer_outputs(results)
print(f"MSA表示均值: {analysis['msa_mean']:.4f}")
print(f"Pair表示对角线均值: {analysis['pair_diagonal_mean']:.4f}")
```

### 4. 参数说明

#### 4.1 EvoformerModel参数

- `esm_dim` (int): ESM-2特征维度，默认1280
- `msa_dim` (int): MSA特征维度，默认256
- `pair_dim` (int): Pair特征维度，默认128
- `num_blocks` (int): Evoformer块数量，默认4
- `num_heads` (int): 注意力头数，默认8
- `dropout` (float): Dropout率，默认0.1

#### 4.2 EvoformerBlock组件

- **MSARowAttention**: MSA行注意力
- **MSAColumnAttention**: MSA列注意力
- **OuterProductMean**: 外积均值操作
- **TriangleMultiplication**: 三角乘法
- **TriangleAttention**: 三角注意力

### 5. 输出说明

模型输出包含以下核心字段：

- `fused_features`: 最终融合特征 (batch_size, seq_len, esm_dim)
- `msa_representation`: MSA表示 (batch_size, num_sequences, seq_len, msa_dim)
- `pair_representation`: Pair表示 (batch_size, seq_len, seq_len, pair_dim)
- `single_representation`: Single表示 (batch_size, seq_len, esm_dim)
- `msa_evolution`: MSA在各层的演化
- `pair_evolution`: Pair在各层的演化
- `single_evolution`: Single在各层的演化

### 6. 高级功能

#### 6.1 表示演化分析

```python
from utils import plot_representation_evolution

# 分析MSA表示演化
plot_representation_evolution(
    results["msa_evolution"],
    "msa",
    save_path="msa_evolution.png"
)

# 分析Pair表示演化
plot_representation_evolution(
    results["pair_evolution"],
    "pair",
    save_path="pair_evolution.png"
)
```

#### 6.2 可视化Pair表示

```python
from utils import visualize_pair_representation

# 可视化最终Pair表示
visualize_pair_representation(
    results["pair_representation"][0],
    sequence,
    title="Final Pair Representation",
    save_path="pair_representation.png"
)
```

#### 6.3 MSA表示可视化

```python
from utils import visualize_msa_representation

# 可视化MSA表示
visualize_msa_representation(
    results["msa_representation"][0],
    sequence,
    max_sequences=50,
    save_path="msa_representation.png"
)
```

### 7. 性能优化

#### 7.1 内存优化

```python
# 减少MSA序列数量
extractor = ProteinFeatureExtractor()
results = model.extract_and_fuse(
    sequence,
    extractor,
    max_msa_sequences=256  # 默认512
)

# 使用较小的模型
model = EvoformerModel(
    esm_dim=1280,
    msa_dim=128,     # 减少MSA维度
    pair_dim=64,     # 减少Pair维度
    num_blocks=2,    # 减少块数
    num_heads=4      # 减少注意力头数
)
```

#### 7.2 计算优化

```python
# 使用混合精度训练
import torch
with torch.cuda.amp.autocast():
    results = model.extract_and_fuse(sequence, extractor)

# 启用编译优化 (PyTorch 2.0+)
model = torch.compile(model)
```

### 8. 故障排除

#### 8.1 常见问题

**Q: CUDA内存不足**
A: 减少序列长度、MSA深度或模型参数；使用CPU计算

**Q: 三角注意力计算错误**
A: 检查输入张量形状，确保seq_len维度一致

**Q: 外积均值操作失败**
A: 验证MSA和Pair表示的维度匹配

#### 8.2 调试技巧

```python
# 检查中间表示
print(f"MSA形状: {msa_repr.shape}")
print(f"Pair形状: {pair_repr.shape}")
print(f"Single形状: {single_repr.shape}")

# 检查数值稳定性
print(f"MSA范围: [{msa_repr.min():.4f}, {msa_repr.max():.4f}]")
print(f"Pair范围: [{pair_repr.min():.4f}, {pair_repr.max():.4f}]")

# 验证对称性
pair_symmetric = torch.allclose(pair_repr, pair_repr.transpose(-1, -2), atol=1e-6)
print(f"Pair表示对称性: {pair_symmetric}")
```

### 9. 最佳实践

#### 9.1 序列长度建议

- 短序列 (<100): 使用完整参数
- 中等序列 (100-200): 适当减少块数和头数
- 长序列 (>200): 考虑分段处理或使用简化版本

#### 9.2 MSA质量要求

- 推荐MSA深度 >100 序列
- 使用高质量数据库 (UniRef30, MGnify)
- 保持序列多样性，避免过度冗余

#### 9.3 计算资源建议

- GPU内存: 至少16GB (推荐32GB+)
- 系统内存: 至少32GB
- 计算时间: 短序列几分钟，长序列可能需要小时级别

### 10. 应用场景

#### 10.1 结构预测

```python
# 提取接触图用于结构预测
structure_pred = model.get_structure_predictions(results["pair_representation"])
contact_map = structure_pred["contact_probabilities"]

# 可以进一步用于：
# - 距离几何算法
# - 分子动力学模拟初始化
# - 蛋白质折叠预测
```

#### 10.2 功能预测

```python
# 使用融合特征进行功能预测
fused_features = results["fused_features"]

# 可以用于：
# - GO功能注释
# - 酶活性预测
# - 蛋白质-蛋白质相互作用
```

### 11. 引用

```bibtex
@article{evoformer_fusion,
  title={Evoformer-Inspired Architectural Fusion of ESM-2 and MSA Features},
  author={Your Name},
  journal={Your Journal},
  year={2024}
}

@article{alphafold2,
  title={Highly accurate protein structure prediction with AlphaFold},
  author={Jumper, John and others},
  journal={Nature},
  year={2021}
}
```

---

更多详细示例和完整代码请参考 `example.py` 文件。
