#!/usr/bin/env python3
"""
完全离线模式测试脚本
直接使用本地模型文件
"""

import os
import torch
import pandas as pd
import numpy as np
from transformers import EsmModel, EsmTokenizer
import logging

# 设置完全离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_local_model_path():
    """获取本地模型路径"""
    base_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D"
    
    # 查找snapshots目录下的实际模型路径
    snapshots_path = os.path.join(base_path, "snapshots")
    if os.path.exists(snapshots_path):
        # 获取第一个snapshot目录
        snapshot_dirs = [d for d in os.listdir(snapshots_path) if os.path.isdir(os.path.join(snapshots_path, d))]
        if snapshot_dirs:
            model_path = os.path.join(snapshots_path, snapshot_dirs[0])
            return model_path
    
    return None

def test_offline_model_loading():
    """测试离线模型加载"""
    print("1. 测试离线模型加载...")
    
    try:
        # 获取本地模型路径
        model_path = get_local_model_path()
        if not model_path:
            print("   [FAIL] 未找到本地模型路径")
            return None, None, None
        
        print(f"   本地模型路径: {model_path}")
        
        # 检查必要文件
        required_files = ['config.json', 'pytorch_model.bin', 'tokenizer_config.json', 'vocab.txt']
        missing_files = []
        for file in required_files:
            if not os.path.exists(os.path.join(model_path, file)):
                missing_files.append(file)
        
        if missing_files:
            print(f"   [FAIL] 缺少文件: {missing_files}")
            return None, None, None
        
        print("   [OK] 所有必要文件存在")
        
        # 加载tokenizer
        tokenizer = EsmTokenizer.from_pretrained(model_path, local_files_only=True)
        print("   [OK] Tokenizer加载成功")
        
        # 加载模型
        model = EsmModel.from_pretrained(model_path, local_files_only=True)
        print("   [OK] 模型加载成功")
        
        # 设置设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        model.eval()
        print(f"   [OK] 模型移动到设备: {device}")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   模型参数量: {total_params:,}")
        
        return tokenizer, model, device
        
    except Exception as e:
        print(f"   [FAIL] 离线模型加载失败: {e}")
        return None, None, None

def test_feature_extraction_offline(tokenizer, model, device):
    """测试离线特征提取"""
    print("\n2. 测试离线特征提取...")
    
    if tokenizer is None or model is None:
        print("   [FAIL] 模型未加载，跳过测试")
        return None
    
    try:
        # 测试序列
        test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        print(f"   测试序列长度: {len(test_sequence)}")
        
        # Tokenize
        inputs = tokenizer(test_sequence, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        print(f"   [OK] Tokenization成功")
        
        # 前向传播
        with torch.no_grad():
            outputs = model(**inputs)
        
        print("   [OK] 前向传播成功")
        
        # 提取特征
        last_hidden_state = outputs.last_hidden_state
        print(f"   输出形状: {last_hidden_state.shape}")
        
        # 去除特殊token
        sequence_features = last_hidden_state[0, 1:-1, :]  # (seq_len, hidden_dim)
        print(f"   序列特征形状: {sequence_features.shape}")
        
        # 序列级池化
        pooled_features = sequence_features.mean(dim=0)  # (hidden_dim,)
        print(f"   池化特征形状: {pooled_features.shape}")
        
        # 转换为numpy
        feature_vector = pooled_features.cpu().numpy()
        print(f"   特征向量维度: {feature_vector.shape[0]}")
        print(f"   特征统计: 均值={feature_vector.mean():.4f}, 标准差={feature_vector.std():.4f}")
        
        return feature_vector
        
    except Exception as e:
        print(f"   [FAIL] 特征提取失败: {e}")
        return None

def test_batch_simulation(tokenizer, model, device):
    """测试批量处理模拟"""
    print("\n3. 测试批量处理模拟...")
    
    if tokenizer is None or model is None:
        print("   [FAIL] 模型未加载，跳过测试")
        return False
    
    try:
        # 加载数据样本
        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')
        sample_data = df[['ID', 'Sequence']].head(3)
        
        print(f"   测试样本数: {len(sample_data)}")
        
        results = []
        for i, row in sample_data.iterrows():
            protein_id = row['ID']
            sequence = row['Sequence']
            
            if pd.isna(sequence) or len(sequence) == 0:
                print(f"   跳过无效序列: {protein_id}")
                continue
            
            if len(sequence) > 500:  # 限制序列长度以节省时间
                print(f"   跳过长序列: {protein_id} (长度: {len(sequence)})")
                continue
            
            print(f"   处理: {protein_id} (长度: {len(sequence)})")
            
            # 提取特征
            inputs = tokenizer(sequence, return_tensors="pt", padding=True, truncation=True)
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model(**inputs)
            
            # 获取序列级特征
            last_hidden_state = outputs.last_hidden_state
            sequence_features = last_hidden_state[0, 1:-1, :]
            pooled_features = sequence_features.mean(dim=0)
            feature_vector = pooled_features.cpu().numpy()
            
            # 保存结果
            result = {
                'ID': protein_id,
                'Sequence': sequence,
                'fusion_vector': ','.join(map(str, feature_vector)),
                'vector_dim': len(feature_vector),
                'sequence_length': len(sequence)
            }
            results.append(result)
            
            print(f"   [OK] {protein_id} 处理完成，特征维度: {len(feature_vector)}")
        
        # 保存测试结果
        if results:
            results_df = pd.DataFrame(results)
            output_file = "test_offline_results.csv"
            results_df.to_csv(output_file, index=False)
            print(f"   [OK] 测试结果保存到: {output_file}")
            
            # 显示结果统计
            print(f"   成功处理: {len(results)} 个样本")
            vector_dims = [r['vector_dim'] for r in results]
            print(f"   特征维度: {vector_dims[0]} (所有样本一致)")
            
            return True
        else:
            print("   ⚠ 没有成功处理的样本")
            return False
        
    except Exception as e:
        print(f"   [FAIL] 批量处理测试失败: {e}")
        return False

def test_memory_cleanup():
    """测试内存清理"""
    print("\n4. 测试内存清理...")
    
    try:
        if torch.cuda.is_available():
            # 显示当前GPU内存使用
            allocated_before = torch.cuda.memory_allocated() / 1024**3
            cached_before = torch.cuda.memory_reserved() / 1024**3
            
            print(f"   清理前 - 已分配: {allocated_before:.2f} GB, 缓存: {cached_before:.2f} GB")
            
            # 清理GPU内存
            torch.cuda.empty_cache()
            
            allocated_after = torch.cuda.memory_allocated() / 1024**3
            cached_after = torch.cuda.memory_reserved() / 1024**3
            
            print(f"   清理后 - 已分配: {allocated_after:.2f} GB, 缓存: {cached_after:.2f} GB")
            print("   [OK] GPU内存清理完成")
        else:
            print("   使用CPU模式，无需GPU内存清理")
        
        return True
        
    except Exception as e:
        print(f"   ⚠ 内存清理测试失败: {e}")
        return True

def main():
    """主测试函数"""
    print("="*60)
    print("完全离线模式功能测试")
    print("="*60)
    
    # 测试离线模型加载
    tokenizer, model, device = test_offline_model_loading()
    
    if tokenizer is None or model is None:
        print("\n[FAIL] 离线模型加载失败，无法继续测试")
        print("请检查模型文件是否完整")
        return
    
    # 测试特征提取
    feature_vector = test_feature_extraction_offline(tokenizer, model, device)
    
    # 测试批量处理
    batch_ok = test_batch_simulation(tokenizer, model, device)
    
    # 测试内存清理
    memory_ok = test_memory_cleanup()
    
    # 汇总结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    model_ok = tokenizer is not None and model is not None
    feature_ok = feature_vector is not None
    
    tests = [
        ("离线模型加载", model_ok),
        ("特征提取", feature_ok),
        ("批量处理模拟", batch_ok),
        ("内存清理", memory_ok)
    ]
    
    passed = 0
    for i, (name, result) in enumerate(tests):
        status = "[OK] 通过" if result else "[FAIL] 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(tests)} 项测试通过")
    
    if passed >= 3:
        print("[OK] 主要功能正常，可以开始正式处理！")
        print("\n下一步:")
        print("1. 运行: python run_fusion.py")
        print("2. 或者运行: python architectural_fusion_processor.py --input Km_Data.xlsx")
    else:
        print("[FAIL] 关键功能测试失败，请检查环境")
    
    print("="*60)

if __name__ == "__main__":
    main()
