#!/usr/bin/env python3
"""
环境检查脚本
检查运行蛋白质特征融合所需的环境和依赖
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("1. Python版本检查")
    version = sys.version_info
    print(f"   当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("   [OK] Python版本符合要求 (>=3.8)")
        return True
    else:
        print("   [FAIL] Python版本过低，需要Python 3.8或更高版本")
        return False

def check_conda_environment():
    """检查conda环境"""
    print("\n2. Conda环境检查")
    
    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"   当前conda环境: {conda_env}")
        if conda_env == 'attention_fusion':
            print("   [OK] 正在使用attention_fusion环境")
            return True
        else:
            print("   ⚠ 建议使用attention_fusion环境")
            print("   请运行: conda activate attention_fusion")
            return False
    else:
        print("   ⚠ 未检测到conda环境")
        print("   请运行: conda activate attention_fusion")
        return False

def check_required_packages():
    """检查必需的Python包"""
    print("\n3. Python包依赖检查")
    
    required_packages = {
        'torch': 'PyTorch',
        'transformers': 'Hugging Face Transformers',
        'pandas': 'Pandas',
        'numpy': 'NumPy',
        'openpyxl': 'OpenPyXL',
        'tqdm': 'tqdm',
        'einops': 'einops',
        'Bio': 'BioPython'
    }
    
    missing_packages = []
    
    for package, description in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"   [OK] {description} ({package})")
        except ImportError:
            print(f"   [FAIL] {description} ({package}) - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   缺少的包: {', '.join(missing_packages)}")
        print("   请安装缺少的包:")
        for pkg in missing_packages:
            if pkg == 'Bio':
                print(f"   pip install biopython")
            else:
                print(f"   pip install {pkg}")
        return False
    
    return True

def check_gpu_availability():
    """检查GPU可用性"""
    print("\n4. GPU检查")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_device)
            
            print(f"   [OK] CUDA可用")
            print(f"   GPU数量: {gpu_count}")
            print(f"   当前GPU: {gpu_name}")
            print(f"   GPU内存: {torch.cuda.get_device_properties(current_device).total_memory / 1024**3:.1f} GB")
            return True
        else:
            print("   ⚠ CUDA不可用，将使用CPU")
            print("   注意: CPU处理会非常慢")
            return False
    except ImportError:
        print("   [FAIL] 无法检查GPU状态 (PyTorch未安装)")
        return False

def check_model_cache():
    """检查模型缓存"""
    print("\n5. 模型缓存检查")
    
    # 检查Hugging Face缓存
    hf_cache_dir = Path.home() / '.cache' / 'huggingface'
    if hf_cache_dir.exists():
        print(f"   [OK] Hugging Face缓存目录存在: {hf_cache_dir}")
        
        # 检查ESM-2模型
        esm_model_path = hf_cache_dir / 'transformers'
        if esm_model_path.exists():
            esm_files = list(esm_model_path.glob('*esm2*'))
            if esm_files:
                print(f"   [OK] 找到ESM-2模型缓存")
            else:
                print(f"   ⚠ 未找到ESM-2模型缓存，首次运行时会自动下载")
    else:
        print(f"   ⚠ Hugging Face缓存目录不存在，首次运行时会创建")
    
    # 检查PyTorch Hub缓存
    torch_cache_dir = Path.home() / '.cache' / 'torch' / 'hub' / 'checkpoints'
    if torch_cache_dir.exists():
        print(f"   [OK] PyTorch Hub缓存目录存在: {torch_cache_dir}")
        
        # 检查MSA Transformer
        msa_files = list(torch_cache_dir.glob('*msa*'))
        if msa_files:
            print(f"   [OK] 找到MSA Transformer缓存")
        else:
            print(f"   ⚠ 未找到MSA Transformer缓存，首次运行时会自动下载")
    else:
        print(f"   ⚠ PyTorch Hub缓存目录不存在，首次运行时会创建")

def check_input_file():
    """检查输入文件"""
    print("\n6. 输入文件检查")
    
    input_file = "Km_Data.xlsx"
    if os.path.exists(input_file):
        file_size = os.path.getsize(input_file) / 1024 / 1024  # MB
        print(f"   [OK] 找到输入文件: {input_file}")
        print(f"   文件大小: {file_size:.2f} MB")
        
        # 尝试读取文件
        try:
            import pandas as pd
            df = pd.read_excel(input_file, engine='openpyxl')
            print(f"   [OK] 文件可读取，包含 {len(df)} 行数据")
            
            # 检查必需的列
            required_cols = ['ID', 'Sequence']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"   [FAIL] 缺少必需的列: {missing_cols}")
                return False
            else:
                print(f"   [OK] 包含必需的列: {required_cols}")
                
                # 检查数据质量
                valid_sequences = df['Sequence'].notna() & (df['Sequence'].str.len() > 0)
                print(f"   有效序列数: {valid_sequences.sum()}")
                
                if valid_sequences.sum() > 0:
                    seq_lengths = df[valid_sequences]['Sequence'].str.len()
                    print(f"   序列长度范围: {seq_lengths.min()} - {seq_lengths.max()}")
                    return True
                else:
                    print(f"   [FAIL] 没有有效的序列数据")
                    return False
                    
        except Exception as e:
            print(f"   [FAIL] 读取文件失败: {e}")
            return False
    else:
        print(f"   [FAIL] 未找到输入文件: {input_file}")
        print("   请确保Km_Data.xlsx文件在当前目录中")
        return False

def check_disk_space():
    """检查磁盘空间"""
    print("\n7. 磁盘空间检查")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free / 1024**3
        
        print(f"   可用磁盘空间: {free_gb:.2f} GB")
        
        if free_gb > 10:
            print("   [OK] 磁盘空间充足")
            return True
        elif free_gb > 5:
            print("   ⚠ 磁盘空间较少，建议清理")
            return True
        else:
            print("   [FAIL] 磁盘空间不足，需要至少5GB空间")
            return False
    except Exception as e:
        print(f"   ⚠ 无法检查磁盘空间: {e}")
        return True

def main():
    """主检查函数"""
    print("="*60)
    print("蛋白质特征融合环境检查")
    print("="*60)
    
    checks = [
        check_python_version(),
        check_conda_environment(),
        check_required_packages(),
        check_gpu_availability(),
        check_input_file(),
        check_disk_space()
    ]
    
    check_model_cache()  # 信息性检查，不影响结果
    
    print("\n" + "="*60)
    print("检查结果汇总")
    print("="*60)
    
    passed = sum(checks)
    total = len(checks)
    
    print(f"通过检查: {passed}/{total}")
    
    if passed == total:
        print("[OK] 所有检查通过，环境配置正确！")
        print("可以运行: python run_fusion.py")
    elif passed >= total - 1:
        print("⚠ 大部分检查通过，可以尝试运行")
        print("如果遇到问题，请根据上述提示进行修复")
    else:
        print("[FAIL] 多项检查未通过，请先修复环境问题")
        print("修复后重新运行此检查脚本")
    
    print("="*60)

if __name__ == "__main__":
    main()
