#!/usr/bin/env python3
"""
增量式ESM-2 + MSA特征融合处理器
每处理一个样本就立即保存，防止数据丢失
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
import gc

from transformers import EsmModel, EsmTokenizer
import esm  # Facebook ESM库，用于MSA模型

# 设置离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('incremental_fusion_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ESM_MSA_FusionModel(nn.Module):
    """ESM-2 + MSA特征融合模型"""
    
    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):
        super().__init__()
        
        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.fusion_dim = fusion_dim
        
        # ESM-2特征投影
        self.esm_projection = nn.Sequential(
            nn.Linear(esm_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # MSA特征投影
        self.msa_projection = nn.Sequential(
            nn.Linear(msa_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 注意力融合机制
        self.attention_fusion = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
    def forward(self, esm_features: torch.Tensor, msa_features: torch.Tensor) -> torch.Tensor:
        # 投影到相同维度
        esm_proj = self.esm_projection(esm_features)
        msa_proj = self.msa_projection(msa_features)
        
        # 注意力融合
        attended_features, _ = self.attention_fusion(esm_proj, msa_proj, msa_proj)
        attended_features = attended_features + esm_proj
        
        # 拼接和最终融合
        concatenated = torch.cat([esm_proj, attended_features], dim=-1)
        fused_features = self.final_fusion(concatenated)
        
        return fused_features


class IncrementalFusionProcessor:
    """增量式融合处理器 - 每处理一个就保存一个"""
    
    def __init__(self, 
                 output_dir: str = ".",
                 max_sequence_length: int = 1024):
        """
        初始化处理器
        
        Args:
            output_dir: 输出目录
            max_sequence_length: 最大序列长度
        """
        self.output_dir = output_dir
        self.max_sequence_length = max_sequence_length
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 文件路径
        self.results_file = os.path.join(output_dir, "architectural_fusion_vector.csv")
        self.checkpoint_file = os.path.join(output_dir, "processing_checkpoint.json")
        
        # 初始化模型
        self._initialize_models()
        
        # 状态跟踪
        self.processed_count = 0
        self.total_count = 0
        
    def _get_local_esm_path(self):
        """获取本地ESM-2模型路径"""
        base_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D"
        snapshots_path = os.path.join(base_path, "snapshots")
        if os.path.exists(snapshots_path):
            snapshot_dirs = [d for d in os.listdir(snapshots_path) 
                           if os.path.isdir(os.path.join(snapshots_path, d))]
            if snapshot_dirs:
                model_path = os.path.join(snapshots_path, snapshot_dirs[0])
                required_files = ['config.json', 'pytorch_model.bin', 'tokenizer_config.json']
                if all(os.path.exists(os.path.join(model_path, f)) for f in required_files):
                    return model_path
        return None
        
    def _initialize_models(self):
        """初始化ESM-2和MSA模型"""
        logger.info("初始化ESM-2和MSA模型...")
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"使用设备: {self.device}")
        
        try:
            # 1. 加载ESM-2模型
            logger.info("加载ESM-2模型...")
            local_esm_path = self._get_local_esm_path()
            if local_esm_path:
                logger.info(f"使用本地ESM-2模型: {local_esm_path}")
                self.esm_tokenizer = EsmTokenizer.from_pretrained(local_esm_path, local_files_only=True)
                self.esm_model = EsmModel.from_pretrained(local_esm_path, local_files_only=True)
            else:
                logger.info("使用在线ESM-2模型")
                model_name = "facebook/esm2_t33_650M_UR50D"
                self.esm_tokenizer = EsmTokenizer.from_pretrained(model_name)
                self.esm_model = EsmModel.from_pretrained(model_name)
            
            self.esm_model.to(self.device)
            self.esm_model.eval()
            logger.info("[OK] ESM-2模型加载成功")
            
            # 2. 加载MSA模型
            logger.info("加载MSA模型...")
            torch.hub.set_dir('/home/<USER>/.cache/torch/hub')
            self.msa_model, self.msa_alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()
            self.msa_model.to(self.device)
            self.msa_model.eval()
            logger.info("[OK] MSA模型加载成功")
            
            # 3. 初始化融合模型
            logger.info("初始化融合模型...")
            esm_dim = self.esm_model.config.hidden_size
            msa_dim = self.msa_model.args.embed_dim
            
            self.fusion_model = ESM_MSA_FusionModel(
                esm_dim=esm_dim,
                msa_dim=msa_dim,
                fusion_dim=1280
            )
            self.fusion_model.to(self.device)
            self.fusion_model.eval()
            logger.info("[OK] 融合模型初始化成功")
            
            # 打印模型信息
            esm_params = sum(p.numel() for p in self.esm_model.parameters())
            msa_params = sum(p.numel() for p in self.msa_model.parameters())
            fusion_params = sum(p.numel() for p in self.fusion_model.parameters())
            
            logger.info(f"ESM-2模型参数量: {esm_params:,}")
            logger.info(f"MSA模型参数量: {msa_params:,}")
            logger.info(f"融合模型参数量: {fusion_params:,}")
            logger.info(f"总参数量: {esm_params + msa_params + fusion_params:,}")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise
    
    def _create_msa_from_sequence(self, sequence: str, num_sequences: int = 64) -> List[str]:
        """从单个序列创建MSA（简化版本）"""
        msa_sequences = [sequence]  # 原序列作为第一条
        
        # 为了演示，添加一些轻微变异的序列
        for i in range(min(num_sequences - 1, 31)):
            msa_sequences.append(sequence)
        
        return msa_sequences
    
    def extract_esm_features(self, sequence: str) -> torch.Tensor:
        """提取ESM-2特征"""
        inputs = self.esm_tokenizer(
            sequence, 
            return_tensors="pt", 
            padding=True, 
            truncation=True,
            max_length=self.max_sequence_length + 2
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.esm_model(**inputs)
        
        features = outputs.last_hidden_state[0, 1:-1, :]
        return features
    
    def extract_msa_features(self, msa_sequences: List[str]) -> torch.Tensor:
        """提取MSA特征"""
        msa_data = []
        for seq in msa_sequences:
            if len(seq) > self.max_sequence_length:
                seq = seq[:self.max_sequence_length]
            msa_data.append(("", seq))
        
        msa_batch_converter = self.msa_alphabet.get_batch_converter()
        msa_batch_labels, msa_batch_strs, msa_batch_tokens = msa_batch_converter([msa_data])
        
        msa_batch_tokens = msa_batch_tokens.to(self.device)
        
        with torch.no_grad():
            results = self.msa_model(msa_batch_tokens, repr_layers=[12])
        
        msa_features = results["representations"][12][0, 0, 1:-1, :]
        return msa_features
    
    def extract_fused_features(self, sequence: str, protein_id: str) -> Optional[np.ndarray]:
        """提取融合特征"""
        try:
            # 检查序列长度
            if len(sequence) > self.max_sequence_length:
                logger.warning(f"序列 {protein_id} 过长 ({len(sequence)} 残基)，截断到 {self.max_sequence_length}")
                sequence = sequence[:self.max_sequence_length]
            
            if len(sequence) < 10:
                logger.warning(f"序列 {protein_id} 过短 ({len(sequence)} 残基)，跳过处理")
                return None
            
            # 1. 提取ESM-2特征
            esm_features = self.extract_esm_features(sequence)
            
            # 2. 创建MSA并提取MSA特征
            msa_sequences = self._create_msa_from_sequence(sequence)
            msa_features = self.extract_msa_features(msa_sequences)
            
            # 3. 确保特征长度一致
            min_len = min(esm_features.size(0), msa_features.size(0))
            esm_features = esm_features[:min_len, :]
            msa_features = msa_features[:min_len, :]
            
            # 4. 添加batch维度
            esm_features = esm_features.unsqueeze(0)
            msa_features = msa_features.unsqueeze(0)
            
            # 5. 特征融合
            with torch.no_grad():
                fused_features = self.fusion_model(esm_features, msa_features)
            
            # 6. 序列级池化（平均）
            pooled_features = fused_features.mean(dim=1).squeeze(0)
            
            # 7. 转换为numpy
            feature_vector = pooled_features.cpu().numpy()
            
            logger.debug(f"成功提取 {protein_id} 的融合特征，维度: {feature_vector.shape}")
            
            return feature_vector
            
        except Exception as e:
            logger.error(f"提取 {protein_id} 融合特征失败: {e}")
            return None
        finally:
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()

    def load_data(self, excel_file: str) -> pd.DataFrame:
        """加载数据"""
        logger.info(f"加载数据文件: {excel_file}")

        try:
            df = pd.read_excel(excel_file, engine='openpyxl')
            logger.info(f"原始数据形状: {df.shape}")

            # 提取需要的列
            data = df[['ID', 'Sequence']].copy()

            # 清理数据
            data = data.dropna(subset=['ID', 'Sequence'])
            data = data[data['Sequence'].str.len() > 0]

            logger.info(f"清理后数据形状: {data.shape}")

            return data

        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise

    def load_processed_ids(self) -> set:
        """加载已处理的ID列表"""
        if os.path.exists(self.results_file):
            try:
                existing_df = pd.read_csv(self.results_file)
                processed_ids = set(existing_df['ID'].astype(str))
                logger.info(f"发现已有结果文件，包含 {len(processed_ids)} 个已处理样本")
                return processed_ids
            except Exception as e:
                logger.warning(f"读取已有结果文件失败: {e}")

        return set()

    def save_single_result(self, result_row: Dict):
        """保存单个结果到CSV文件"""
        try:
            # 转换为DataFrame
            new_df = pd.DataFrame([result_row])

            # 如果文件不存在，创建新文件（包含表头）
            if not os.path.exists(self.results_file):
                new_df.to_csv(self.results_file, index=False)
                logger.info(f"创建新结果文件: {self.results_file}")
            else:
                # 追加到现有文件（不包含表头）
                new_df.to_csv(self.results_file, mode='a', header=False, index=False)

            logger.debug(f"保存结果: {result_row['ID']}")

        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise

    def save_checkpoint(self, processed_count: int, current_id: str):
        """保存检查点"""
        checkpoint = {
            "processed_count": processed_count,
            "current_id": current_id,
            "timestamp": datetime.now().isoformat(),
            "results_file": self.results_file
        }

        try:
            with open(self.checkpoint_file, 'w') as f:
                json.dump(checkpoint, f, indent=2)
            logger.debug(f"保存检查点: 已处理 {processed_count} 个样本")
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")

    def process_incremental(self, data: pd.DataFrame):
        """增量式处理 - 每处理一个就保存一个"""
        # 加载已处理的ID
        processed_ids = self.load_processed_ids()

        # 过滤已处理的数据
        remaining_data = data[~data['ID'].astype(str).isin(processed_ids)].reset_index(drop=True)
        self.total_count = len(data)
        self.processed_count = len(processed_ids)

        logger.info(f"总样本数: {self.total_count}")
        logger.info(f"已处理: {self.processed_count}")
        logger.info(f"待处理: {len(remaining_data)}")

        if len(remaining_data) == 0:
            logger.info("所有样本已处理完成！")
            return

        # 逐个处理剩余数据
        for idx, row in tqdm(remaining_data.iterrows(),
                           total=len(remaining_data),
                           desc="增量处理ESM-2+MSA融合特征"):

            protein_id = str(row['ID'])
            sequence = row['Sequence']

            logger.info(f"处理 {protein_id} (序列长度: {len(sequence)})")

            # 提取融合特征
            feature_vector = self.extract_fused_features(sequence, protein_id)

            # 准备结果行
            if feature_vector is not None:
                # 成功提取特征
                fusion_vector_str = ','.join(map(str, feature_vector))

                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': fusion_vector_str,
                    'vector_dim': len(feature_vector),
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat(),
                    'fusion_type': 'ESM2_MSA',
                    'status': 'SUCCESS'
                }

                logger.info(f"[OK] {protein_id} 处理成功，特征维度: {len(feature_vector)}")

            else:
                # 处理失败
                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': None,
                    'vector_dim': None,
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat(),
                    'fusion_type': 'ESM2_MSA',
                    'status': 'FAILED'
                }

                logger.warning(f"[FAIL] {protein_id} 处理失败")

            # 立即保存单个结果
            self.save_single_result(result_row)

            # 更新计数
            self.processed_count += 1

            # 保存检查点
            self.save_checkpoint(self.processed_count, protein_id)

            # 显示进度
            if self.processed_count % 10 == 0:
                logger.info(f"进度: {self.processed_count}/{self.total_count} ({self.processed_count/self.total_count*100:.1f}%)")

        logger.info(f"增量处理完成！总共处理了 {self.processed_count} 个样本")

    def get_processing_stats(self) -> Dict:
        """获取处理统计信息"""
        stats = {
            "total_samples": self.total_count,
            "processed_samples": self.processed_count,
            "remaining_samples": self.total_count - self.processed_count,
            "progress_percentage": (self.processed_count / self.total_count * 100) if self.total_count > 0 else 0
        }

        if os.path.exists(self.results_file):
            try:
                results_df = pd.read_csv(self.results_file)
                successful_count = results_df['status'].eq('SUCCESS').sum()
                failed_count = results_df['status'].eq('FAILED').sum()

                stats.update({
                    "successful_extractions": int(successful_count),
                    "failed_extractions": int(failed_count),
                    "success_rate": (successful_count / len(results_df) * 100) if len(results_df) > 0 else 0
                })
            except Exception as e:
                logger.warning(f"获取统计信息失败: {e}")

        return stats


def main():
    """主函数"""
    print("="*80)
    print("增量式ESM-2 + MSA特征融合处理器")
    print("每处理一个样本就立即保存，防止数据丢失")
    print("="*80)

    # 检查输入文件
    input_file = "Km_Data.xlsx"
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return

    try:
        # 初始化处理器
        print("\n初始化增量式融合处理器...")
        processor = IncrementalFusionProcessor(
            output_dir=".",  # 直接保存到当前目录
            max_sequence_length=1024
        )

        # 加载数据
        data = processor.load_data(input_file)

        print(f"\n数据概览:")
        print(f"总样本数: {len(data)}")

        # 序列长度统计
        seq_lengths = data['Sequence'].str.len()
        print(f"序列长度统计:")
        print(f"  最小: {seq_lengths.min()}")
        print(f"  最大: {seq_lengths.max()}")
        print(f"  平均: {seq_lengths.mean():.1f}")
        print(f"  中位数: {seq_lengths.median():.1f}")

        # 检查可处理的序列
        valid_seqs = (seq_lengths >= 10) & (seq_lengths <= 1024)
        long_seqs = seq_lengths > 1024
        short_seqs = seq_lengths < 10

        print(f"  可处理序列数: {valid_seqs.sum()}")
        print(f"  过长序列 (>1024，将截断): {long_seqs.sum()}")
        print(f"  过短序列 (<10，将跳过): {short_seqs.sum()}")

        # 检查已处理的样本
        processed_ids = processor.load_processed_ids()
        remaining_count = len(data) - len(processed_ids)

        print(f"\n处理状态:")
        print(f"  已处理样本: {len(processed_ids)}")
        print(f"  待处理样本: {remaining_count}")

        if remaining_count == 0:
            print("[OK] 所有样本已处理完成！")

            # 显示统计信息
            if os.path.exists("architectural_fusion_vector.csv"):
                results_df = pd.read_csv("architectural_fusion_vector.csv")
                successful = results_df['status'].eq('SUCCESS').sum()
                failed = results_df['status'].eq('FAILED').sum()
                print(f"\n最终统计:")
                print(f"  成功处理: {successful}")
                print(f"  处理失败: {failed}")
                print(f"  成功率: {successful / len(results_df) * 100:.1f}%")
            return

        # 显示前几个待处理样本
        remaining_data = data[~data['ID'].astype(str).isin(processed_ids)]
        print(f"\n前3个待处理样本:")
        for i, row in remaining_data.head(3).iterrows():
            seq_len = len(row['Sequence']) if pd.notna(row['Sequence']) else 0
            status = "可处理" if 10 <= seq_len <= 1024 else ("截断" if seq_len > 1024 else "跳过")
            print(f"  {row['ID']}: 长度={seq_len}, 状态={status}")

        # 询问是否继续
        print(f"\n准备增量处理 {remaining_count} 个剩余样本")
        print("特点:")
        print("  [OK] 每处理一个样本就立即保存到 architectural_fusion_vector.csv")
        print("  [OK] 支持随时中断和恢复")
        print("  [OK] 防止数据丢失")
        print("  [OK] 实时进度跟踪")

        response = input("\n是否开始/继续处理? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("处理已取消")
            return

        # 开始增量处理
        print(f"\n开始增量处理...")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"输出文件: architectural_fusion_vector.csv")
        print("注意: 可以随时按 Ctrl+C 中断，已处理的数据不会丢失")

        # 增量处理
        processor.process_incremental(data)

        # 显示最终结果
        print(f"\n处理完成!")
        stats = processor.get_processing_stats()

        print(f"\n最终统计:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")

        print(f"\n输出文件: architectural_fusion_vector.csv")
        print(f"检查点文件: processing_checkpoint.json")
        print(f"日志文件: incremental_fusion_processing.log")

        print(f"\n处理完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    except KeyboardInterrupt:
        print(f"\n\n处理被用户中断")
        print("[OK] 已处理的数据已保存到 architectural_fusion_vector.csv")
        print("[OK] 检查点已保存，可以稍后继续处理")
        print("要继续处理，请重新运行此脚本")

    except Exception as e:
        print(f"\n处理过程中出现错误: {e}")
        logger.error(f"处理错误: {e}", exc_info=True)


if __name__ == "__main__":
    main()
