#!/usr/bin/env python3
"""
真正的ESM-2 + MSA特征融合处理器
使用本地模型，支持最大序列长度
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
import gc

from transformers import EsmModel, EsmTokenizer
import esm  # Facebook ESM库，用于MSA模型

# 设置离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('true_fusion_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ESM_MSA_FusionModel(nn.Module):
    """ESM-2 + MSA特征融合模型"""
    
    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):
        """
        初始化融合模型
        
        Args:
            esm_dim: ESM-2特征维度
            msa_dim: MSA特征维度  
            fusion_dim: 融合后特征维度
        """
        super().__init__()
        
        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.fusion_dim = fusion_dim
        
        # ESM-2特征投影
        self.esm_projection = nn.Sequential(
            nn.Linear(esm_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # MSA特征投影
        self.msa_projection = nn.Sequential(
            nn.Linear(msa_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 注意力融合机制
        self.attention_fusion = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
    def forward(self, esm_features: torch.Tensor, msa_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            esm_features: ESM-2特征 (batch_size, seq_len, esm_dim)
            msa_features: MSA特征 (batch_size, seq_len, msa_dim)
            
        Returns:
            融合特征 (batch_size, seq_len, fusion_dim)
        """
        # 投影到相同维度
        esm_proj = self.esm_projection(esm_features)  # (batch_size, seq_len, fusion_dim)
        msa_proj = self.msa_projection(msa_features)  # (batch_size, seq_len, fusion_dim)
        
        # 注意力融合
        # 使用ESM特征作为query，MSA特征作为key和value
        attended_features, _ = self.attention_fusion(esm_proj, msa_proj, msa_proj)
        
        # 残差连接
        attended_features = attended_features + esm_proj
        
        # 拼接原始投影特征和注意力特征
        concatenated = torch.cat([esm_proj, attended_features], dim=-1)
        
        # 最终融合
        fused_features = self.final_fusion(concatenated)
        
        return fused_features


class TrueFusionProcessor:
    """真正的ESM-2 + MSA融合处理器"""
    
    def __init__(self, 
                 output_dir: str = "true_fusion_output",
                 checkpoint_interval: int = 5,
                 max_sequence_length: int = 1024):
        """
        初始化处理器
        
        Args:
            output_dir: 输出目录
            checkpoint_interval: 检查点保存间隔
            max_sequence_length: 最大序列长度
        """
        self.output_dir = output_dir
        self.checkpoint_interval = checkpoint_interval
        self.max_sequence_length = max_sequence_length
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化模型
        self._initialize_models()
        
        # 状态跟踪
        self.processed_count = 0
        self.total_count = 0
        self.checkpoint_file = os.path.join(output_dir, "processing_checkpoint.json")
        self.results_file = os.path.join(output_dir, "architectural_fusion_vectors.csv")
        
    def _get_local_esm_path(self):
        """获取本地ESM-2模型路径"""
        base_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D"
        snapshots_path = os.path.join(base_path, "snapshots")
        if os.path.exists(snapshots_path):
            snapshot_dirs = [d for d in os.listdir(snapshots_path) 
                           if os.path.isdir(os.path.join(snapshots_path, d))]
            if snapshot_dirs:
                model_path = os.path.join(snapshots_path, snapshot_dirs[0])
                required_files = ['config.json', 'pytorch_model.bin', 'tokenizer_config.json']
                if all(os.path.exists(os.path.join(model_path, f)) for f in required_files):
                    return model_path
        return None
        
    def _initialize_models(self):
        """初始化ESM-2和MSA模型"""
        logger.info("初始化ESM-2和MSA模型...")
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"使用设备: {self.device}")
        
        try:
            # 1. 加载ESM-2模型
            logger.info("加载ESM-2模型...")
            local_esm_path = self._get_local_esm_path()
            if local_esm_path:
                logger.info(f"使用本地ESM-2模型: {local_esm_path}")
                self.esm_tokenizer = EsmTokenizer.from_pretrained(local_esm_path, local_files_only=True)
                self.esm_model = EsmModel.from_pretrained(local_esm_path, local_files_only=True)
            else:
                logger.info("使用在线ESM-2模型")
                model_name = "facebook/esm2_t33_650M_UR50D"
                self.esm_tokenizer = EsmTokenizer.from_pretrained(model_name)
                self.esm_model = EsmModel.from_pretrained(model_name)
            
            self.esm_model.to(self.device)
            self.esm_model.eval()
            logger.info("✓ ESM-2模型加载成功")
            
            # 2. 加载MSA模型
            logger.info("加载MSA模型...")
            # 设置PyTorch Hub缓存目录
            torch.hub.set_dir('/home/<USER>/.cache/torch/hub')
            
            # 加载MSA模型（使用本地缓存）
            self.msa_model, self.msa_alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()
            self.msa_model.to(self.device)
            self.msa_model.eval()
            logger.info("✓ MSA模型加载成功")
            
            # 3. 初始化融合模型
            logger.info("初始化融合模型...")
            esm_dim = self.esm_model.config.hidden_size  # 1280 for esm2_t33_650M
            msa_dim = self.msa_model.args.embed_dim  # MSA模型的嵌入维度 (768)
            
            self.fusion_model = ESM_MSA_FusionModel(
                esm_dim=esm_dim,
                msa_dim=msa_dim,
                fusion_dim=1280  # 输出维度
            )
            self.fusion_model.to(self.device)
            self.fusion_model.eval()
            logger.info("✓ 融合模型初始化成功")
            
            # 打印模型信息
            esm_params = sum(p.numel() for p in self.esm_model.parameters())
            msa_params = sum(p.numel() for p in self.msa_model.parameters())
            fusion_params = sum(p.numel() for p in self.fusion_model.parameters())
            
            logger.info(f"ESM-2模型参数量: {esm_params:,}")
            logger.info(f"MSA模型参数量: {msa_params:,}")
            logger.info(f"融合模型参数量: {fusion_params:,}")
            logger.info(f"总参数量: {esm_params + msa_params + fusion_params:,}")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise
    
    def _create_msa_from_sequence(self, sequence: str, num_sequences: int = 64) -> List[str]:
        """
        从单个序列创建MSA（简化版本）
        在实际应用中，这里应该使用真正的MSA搜索
        """
        # 简化版本：创建包含原序列的MSA
        # 在实际应用中，应该使用HHblits或MMseqs2等工具搜索同源序列
        msa_sequences = [sequence]  # 原序列作为第一条
        
        # 为了演示，我们添加一些轻微变异的序列
        # 实际应用中应该使用真正的同源序列
        for i in range(min(num_sequences - 1, 31)):  # 限制MSA大小
            # 这里只是简单复制原序列，实际应该是同源序列
            msa_sequences.append(sequence)
        
        return msa_sequences
    
    def extract_esm_features(self, sequence: str) -> torch.Tensor:
        """提取ESM-2特征"""
        # Tokenize
        inputs = self.esm_tokenizer(
            sequence, 
            return_tensors="pt", 
            padding=True, 
            truncation=True,
            max_length=self.max_sequence_length + 2  # +2 for CLS and SEP
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # 前向传播
        with torch.no_grad():
            outputs = self.esm_model(**inputs)
        
        # 提取特征（去除CLS和SEP token）
        features = outputs.last_hidden_state[0, 1:-1, :]  # (seq_len, hidden_dim)
        
        return features
    
    def extract_msa_features(self, msa_sequences: List[str]) -> torch.Tensor:
        """提取MSA特征"""
        # 准备MSA数据
        msa_data = []
        for seq in msa_sequences:
            # 截断序列以适应模型限制
            if len(seq) > self.max_sequence_length:
                seq = seq[:self.max_sequence_length]
            msa_data.append(("", seq))
        
        # 使用MSA alphabet进行编码
        msa_batch_converter = self.msa_alphabet.get_batch_converter()
        msa_batch_labels, msa_batch_strs, msa_batch_tokens = msa_batch_converter([msa_data])
        
        msa_batch_tokens = msa_batch_tokens.to(self.device)
        
        # 前向传播
        with torch.no_grad():
            results = self.msa_model(msa_batch_tokens, repr_layers=[12])
        
        # 提取特征（取第一个序列的表示，即查询序列）
        # MSA模型输出: (batch_size, num_sequences, seq_len, hidden_dim)
        msa_features = results["representations"][12][0, 0, 1:-1, :]  # 第一个序列，去除特殊token
        
        return msa_features

    def extract_fused_features(self, sequence: str, protein_id: str) -> Optional[np.ndarray]:
        """提取融合特征"""
        try:
            # 检查序列长度
            if len(sequence) > self.max_sequence_length:
                logger.warning(f"序列 {protein_id} 过长 ({len(sequence)} 残基)，截断到 {self.max_sequence_length}")
                sequence = sequence[:self.max_sequence_length]

            if len(sequence) < 10:
                logger.warning(f"序列 {protein_id} 过短 ({len(sequence)} 残基)，跳过处理")
                return None

            logger.debug(f"处理序列 {protein_id}，长度: {len(sequence)}")

            # 1. 提取ESM-2特征
            esm_features = self.extract_esm_features(sequence)  # (seq_len, esm_dim)

            # 2. 创建MSA并提取MSA特征
            msa_sequences = self._create_msa_from_sequence(sequence)
            msa_features = self.extract_msa_features(msa_sequences)  # (seq_len, msa_dim)

            # 3. 确保特征长度一致
            min_len = min(esm_features.size(0), msa_features.size(0))
            esm_features = esm_features[:min_len, :]
            msa_features = msa_features[:min_len, :]

            # 4. 添加batch维度
            esm_features = esm_features.unsqueeze(0)  # (1, seq_len, esm_dim)
            msa_features = msa_features.unsqueeze(0)  # (1, seq_len, msa_dim)

            # 5. 特征融合
            with torch.no_grad():
                fused_features = self.fusion_model(esm_features, msa_features)  # (1, seq_len, fusion_dim)

            # 6. 序列级池化（平均）
            pooled_features = fused_features.mean(dim=1).squeeze(0)  # (fusion_dim,)

            # 7. 转换为numpy
            feature_vector = pooled_features.cpu().numpy()

            logger.debug(f"成功提取 {protein_id} 的融合特征，维度: {feature_vector.shape}")

            return feature_vector

        except Exception as e:
            logger.error(f"提取 {protein_id} 融合特征失败: {e}")
            return None
        finally:
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()

    def load_data(self, excel_file: str) -> pd.DataFrame:
        """加载数据"""
        logger.info(f"加载数据文件: {excel_file}")

        try:
            df = pd.read_excel(excel_file, engine='openpyxl')
            logger.info(f"原始数据形状: {df.shape}")

            # 提取需要的列
            data = df[['ID', 'Sequence']].copy()

            # 清理数据
            data = data.dropna(subset=['ID', 'Sequence'])
            data = data[data['Sequence'].str.len() > 0]

            logger.info(f"清理后数据形状: {data.shape}")

            return data

        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise

    def load_checkpoint(self) -> Dict:
        """加载检查点"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r') as f:
                    checkpoint = json.load(f)
                logger.info(f"加载检查点: 已处理 {checkpoint.get('processed_count', 0)} 个样本")
                return checkpoint
            except Exception as e:
                logger.warning(f"加载检查点失败: {e}")

        return {"processed_count": 0, "processed_ids": []}

    def save_checkpoint(self, processed_count: int, processed_ids: List):
        """保存检查点"""
        checkpoint = {
            "processed_count": processed_count,
            "processed_ids": processed_ids,
            "timestamp": datetime.now().isoformat()
        }

        try:
            with open(self.checkpoint_file, 'w') as f:
                json.dump(checkpoint, f, indent=2)
            logger.info(f"保存检查点: 已处理 {processed_count} 个样本")
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")

    def process_batch(self, data: pd.DataFrame) -> pd.DataFrame:
        """批量处理"""
        # 加载检查点
        checkpoint = self.load_checkpoint()
        processed_ids = set(checkpoint.get("processed_ids", []))
        start_index = checkpoint.get("processed_count", 0)

        # 过滤已处理的数据
        remaining_data = data[~data['ID'].isin(processed_ids)].reset_index(drop=True)
        self.total_count = len(data)
        self.processed_count = start_index

        logger.info(f"总样本数: {self.total_count}")
        logger.info(f"已处理: {self.processed_count}")
        logger.info(f"待处理: {len(remaining_data)}")

        # 准备结果存储
        results = []

        # 如果存在已保存的结果文件，加载它
        if os.path.exists(self.results_file):
            try:
                existing_results = pd.read_csv(self.results_file)
                logger.info(f"加载已有结果文件，包含 {len(existing_results)} 条记录")
            except Exception as e:
                logger.warning(f"加载已有结果文件失败: {e}")
                existing_results = pd.DataFrame()
        else:
            existing_results = pd.DataFrame()

        # 处理剩余数据
        for idx, row in tqdm(remaining_data.iterrows(),
                           total=len(remaining_data),
                           desc="处理ESM-2+MSA融合特征"):

            protein_id = row['ID']
            sequence = row['Sequence']

            logger.info(f"处理 {protein_id} (序列长度: {len(sequence)})")

            # 提取融合特征
            feature_vector = self.extract_fused_features(sequence, protein_id)

            if feature_vector is not None:
                # 将特征向量转换为字符串
                fusion_vector_str = ','.join(map(str, feature_vector))

                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': fusion_vector_str,
                    'vector_dim': len(feature_vector),
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat(),
                    'fusion_type': 'ESM2_MSA'
                }

                results.append(result_row)
                processed_ids.add(protein_id)
                self.processed_count += 1

                logger.info(f"✓ {protein_id} 处理完成，融合特征维度: {len(feature_vector)}")

            else:
                logger.warning(f"✗ {protein_id} 处理失败")
                # 记录失败的样本
                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': None,
                    'vector_dim': None,
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat(),
                    'fusion_type': 'FAILED'
                }
                results.append(result_row)
                processed_ids.add(protein_id)
                self.processed_count += 1

            # 定期保存结果和检查点
            if len(results) >= self.checkpoint_interval:
                self._save_intermediate_results(results, existing_results)
                self.save_checkpoint(self.processed_count, list(processed_ids))
                results = []  # 清空临时结果

                # 重新加载已有结果
                if os.path.exists(self.results_file):
                    existing_results = pd.read_csv(self.results_file)

        # 保存最后的结果
        if results:
            self._save_intermediate_results(results, existing_results)
            self.save_checkpoint(self.processed_count, list(processed_ids))

        # 加载最终结果
        final_results = pd.read_csv(self.results_file)
        logger.info(f"处理完成！总共处理了 {len(final_results)} 个样本")

        return final_results

    def _save_intermediate_results(self, new_results: List[Dict], existing_results: pd.DataFrame):
        """保存中间结果"""
        try:
            # 转换新结果为DataFrame
            new_df = pd.DataFrame(new_results)

            # 合并结果
            if not existing_results.empty:
                combined_results = pd.concat([existing_results, new_df], ignore_index=True)
            else:
                combined_results = new_df

            # 去重（基于ID）
            combined_results = combined_results.drop_duplicates(subset=['ID'], keep='last')

            # 保存到CSV
            combined_results.to_csv(self.results_file, index=False)
            logger.info(f"保存中间结果: {len(combined_results)} 条记录")

        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")


def test_fusion_models():
    """测试融合模型加载和基本功能"""
    print("="*60)
    print("测试ESM-2 + MSA融合模型")
    print("="*60)

    try:
        # 初始化处理器
        processor = TrueFusionProcessor(output_dir="test_fusion_output")

        # 测试序列
        test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        test_id = "TEST_FUSION_001"

        print(f"测试序列: {test_sequence[:30]}...")
        print(f"序列长度: {len(test_sequence)}")

        # 提取融合特征
        print("\n提取融合特征...")
        feature_vector = processor.extract_fused_features(test_sequence, test_id)

        if feature_vector is not None:
            print("✓ 融合特征提取成功！")
            print(f"  特征维度: {feature_vector.shape[0]}")
            print(f"  特征范围: [{feature_vector.min():.4f}, {feature_vector.max():.4f}]")
            print(f"  特征均值: {feature_vector.mean():.4f}")
            print(f"  特征标准差: {feature_vector.std():.4f}")

            # 保存测试结果
            test_result = {
                'ID': test_id,
                'Sequence': test_sequence,
                'fusion_vector': ','.join(map(str, feature_vector)),
                'vector_dim': len(feature_vector),
                'sequence_length': len(test_sequence),
                'fusion_type': 'ESM2_MSA'
            }

            test_df = pd.DataFrame([test_result])
            test_output = "test_fusion_result.csv"
            test_df.to_csv(test_output, index=False)
            print(f"  测试结果保存到: {test_output}")

            return True
        else:
            print("✗ 融合特征提取失败")
            return False

    except Exception as e:
        print(f"✗ 测试失败: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)
        return False


def main():
    """主函数"""
    print("="*80)
    print("真正的ESM-2 + MSA特征融合处理器")
    print("使用本地模型，支持最大序列长度")
    print("="*80)

    # 检查输入文件
    input_file = "Km_Data.xlsx"
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return

    # 询问是否先运行测试
    test_response = input("是否先运行融合模型测试? (y/N): ").strip().lower()
    if test_response in ['y', 'yes']:
        print("\n运行融合模型测试...")
        test_success = test_fusion_models()
        if not test_success:
            print("测试失败，建议检查环境后再运行正式处理")
            return
        print("\n测试成功！")

    try:
        # 初始化处理器
        print("\n初始化融合处理器...")
        processor = TrueFusionProcessor(
            output_dir="true_fusion_output",
            checkpoint_interval=3,  # 更频繁的检查点，因为处理较慢
            max_sequence_length=1024  # 支持最大序列长度
        )

        # 加载数据
        data = processor.load_data(input_file)

        print(f"\n数据概览:")
        print(f"总样本数: {len(data)}")

        # 序列长度统计
        seq_lengths = data['Sequence'].str.len()
        print(f"序列长度统计:")
        print(f"  最小: {seq_lengths.min()}")
        print(f"  最大: {seq_lengths.max()}")
        print(f"  平均: {seq_lengths.mean():.1f}")
        print(f"  中位数: {seq_lengths.median():.1f}")

        # 检查可处理的序列
        valid_seqs = (seq_lengths >= 10) & (seq_lengths <= 1024)
        long_seqs = seq_lengths > 1024
        short_seqs = seq_lengths < 10

        print(f"  可处理序列数: {valid_seqs.sum()}")
        print(f"  过长序列 (>1024，将截断): {long_seqs.sum()}")
        print(f"  过短序列 (<10，将跳过): {short_seqs.sum()}")

        # 显示前几个样本
        print(f"\n前3个样本:")
        for i, row in data.head(3).iterrows():
            seq_len = len(row['Sequence']) if pd.notna(row['Sequence']) else 0
            status = "可处理" if 10 <= seq_len <= 1024 else ("截断" if seq_len > 1024 else "跳过")
            print(f"  {i+1}. ID: {row['ID']}, 长度: {seq_len}, 状态: {status}")

        # 询问是否继续
        processable_count = valid_seqs.sum() + long_seqs.sum()  # 包括需要截断的
        print(f"\n准备处理 {processable_count} 个序列（包括 {long_seqs.sum()} 个需要截断的长序列）")
        print("注意: ESM-2+MSA融合处理比较耗时，每个序列可能需要几分钟")

        response = input("是否继续? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("处理已取消")
            return

        # 开始处理
        print(f"\n开始处理...")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("注意: 可以随时按 Ctrl+C 中断，进度会自动保存")

        # 批量处理
        results = processor.process_batch(data)

        # 显示结果
        print(f"\n处理完成!")
        successful = results['fusion_vector'].notna().sum()
        failed = results['fusion_vector'].isna().sum()

        print(f"成功处理: {successful} 个")
        print(f"处理失败: {failed} 个")
        print(f"成功率: {successful / len(results) * 100:.1f}%")

        output_file = os.path.join("true_fusion_output", "architectural_fusion_vectors.csv")
        print(f"\n输出文件: {output_file}")

        # 显示特征统计
        if successful > 0:
            fusion_results = results[results['fusion_vector'].notna()]
            vector_dims = fusion_results['vector_dim'].iloc[0]
            print(f"融合特征维度: {vector_dims}")
            print(f"融合类型: ESM-2 + MSA")

        print(f"\n处理完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    except KeyboardInterrupt:
        print(f"\n\n处理被用户中断")
        print("进度已保存，可以稍后继续处理")
        print("要继续处理，请重新运行此脚本")

    except Exception as e:
        print(f"\n处理过程中出现错误: {e}")
        logger.error(f"处理错误: {e}", exc_info=True)


if __name__ == "__main__":
    main()
