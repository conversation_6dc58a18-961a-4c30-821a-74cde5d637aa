#!/usr/bin/env python3
"""
seq2attention使用示例
展示如何在其他程序中调用seq2attention函数
"""

import numpy as np
import pandas as pd
from seq2attention import seq2attention, get_vector_dimension, is_cuda_available, clear_cache


def example_single_sequence():
    """示例1: 处理单个蛋白质序列"""
    print("示例1: 处理单个蛋白质序列")
    print("-" * 40)
    
    # 测试序列
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    
    print(f"输入序列: {sequence[:30]}...")
    print(f"序列长度: {len(sequence)}")
    
    # 提取融合向量
    vector = seq2attention(sequence)
    
    if vector is not None:
        print(f"[OK] 成功提取融合向量")
        print(f"向量维度: {len(vector)}")
        print(f"向量类型: {type(vector)}")
        print(f"向量统计: 均值={vector.mean():.4f}, 标准差={vector.std():.4f}")
        print(f"向量预览: {vector[:5]}")
        
        # 可以将向量用于下游任务
        # 例如：相似性计算、分类、聚类等
        
    else:
        print("[FAIL] 向量提取失败")
    
    print()


def example_multiple_sequences():
    """示例2: 批量处理多个序列"""
    print("示例2: 批量处理多个序列")
    print("-" * 40)
    
    # 多个测试序列
    sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "AKLMNPQRSTUVWXYZAKLMNPQRSTUVWXYZAKLMNPQRSTUVWXYZ",
        "DEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ"
    ]
    
    print(f"输入序列数: {len(sequences)}")
    for i, seq in enumerate(sequences):
        print(f"  序列{i+1}: {seq[:20]}... (长度: {len(seq)})")
    
    # 批量提取融合向量
    vectors = seq2attention(sequences)
    
    print(f"\n处理结果:")
    successful_count = 0
    for i, vector in enumerate(vectors):
        if vector is not None:
            print(f"  序列{i+1}: [OK] 成功 (维度: {len(vector)})")
            successful_count += 1
        else:
            print(f"  序列{i+1}: [FAIL] 失败")
    
    print(f"成功率: {successful_count}/{len(sequences)} ({successful_count/len(sequences)*100:.1f}%)")
    print()


def example_dataframe_processing():
    """示例3: 处理DataFrame中的序列数据"""
    print("示例3: 处理DataFrame中的序列数据")
    print("-" * 40)
    
    # 创建示例数据
    data = {
        'protein_id': ['P001', 'P002', 'P003'],
        'protein_name': ['Protein A', 'Protein B', 'Protein C'],
        'sequence': [
            "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
            "AKLMNPQRSTUVWXYZAKLMNPQRSTUVWXYZAKLMNPQRSTUVWXYZ",
            "DEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ"
        ]
    }
    
    df = pd.DataFrame(data)
    print("输入DataFrame:")
    print(df[['protein_id', 'protein_name']].to_string(index=False))
    
    # 批量提取向量
    print(f"\n提取融合向量...")
    vectors = seq2attention(df['sequence'].tolist())
    
    # 将向量添加到DataFrame
    df['fusion_vector'] = vectors
    df['vector_success'] = df['fusion_vector'].apply(lambda x: x is not None)
    
    print(f"处理结果:")
    for i, row in df.iterrows():
        status = "[OK] 成功" if row['vector_success'] else "[FAIL] 失败"
        vector_info = f"(维度: {len(row['fusion_vector'])})" if row['vector_success'] else ""
        print(f"  {row['protein_id']}: {status} {vector_info}")
    
    # 保存结果（可选）
    # df.to_csv('protein_vectors.csv', index=False)
    
    print()


def example_similarity_calculation():
    """示例4: 计算序列相似性"""
    print("示例4: 计算序列相似性")
    print("-" * 40)
    
    # 两个相似的序列（模拟野生型和突变型）
    wild_type = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    mutant = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"  # 这里可以是突变序列
    
    print(f"野生型序列: {wild_type[:30]}...")
    print(f"突变型序列: {mutant[:30]}...")
    
    # 提取向量
    vector1 = seq2attention(wild_type)
    vector2 = seq2attention(mutant)
    
    if vector1 is not None and vector2 is not None:
        # 计算余弦相似性
        cosine_sim = np.dot(vector1, vector2) / (np.linalg.norm(vector1) * np.linalg.norm(vector2))
        
        # 计算欧氏距离
        euclidean_dist = np.linalg.norm(vector1 - vector2)
        
        print(f"[OK] 相似性分析:")
        print(f"  余弦相似性: {cosine_sim:.4f}")
        print(f"  欧氏距离: {euclidean_dist:.4f}")
        
        # 相似性解释
        if cosine_sim > 0.95:
            print(f"  结论: 序列高度相似")
        elif cosine_sim > 0.8:
            print(f"  结论: 序列中等相似")
        else:
            print(f"  结论: 序列相似性较低")
    else:
        print("[FAIL] 向量提取失败，无法计算相似性")
    
    print()


def example_error_handling():
    """示例5: 错误处理"""
    print("示例5: 错误处理")
    print("-" * 40)
    
    # 测试各种边界情况
    test_cases = [
        ("正常序列", "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"),
        ("短序列", "MKT"),
        ("空序列", ""),
        ("非法字符", "MKTVRQXYZ123"),
        ("超长序列", "M" * 2000)
    ]
    
    for case_name, sequence in test_cases:
        print(f"测试 {case_name}: ", end="")
        
        try:
            vector = seq2attention(sequence)
            if vector is not None:
                print(f"[OK] 成功 (维度: {len(vector)})")
            else:
                print("[FAIL] 返回None")
        except Exception as e:
            print(f"[FAIL] 异常: {e}")
    
    print()


def main():
    """主函数"""
    print("="*60)
    print("seq2attention函数使用示例")
    print("="*60)
    
    # 显示系统信息
    print(f"CUDA可用: {is_cuda_available()}")
    print(f"融合向量维度: {get_vector_dimension()}")
    print()
    
    # 运行各种示例
    example_single_sequence()
    example_multiple_sequences()
    example_dataframe_processing()
    example_similarity_calculation()
    example_error_handling()
    
    # 清理缓存
    print("清理模型缓存...")
    clear_cache()
    print("[OK] 缓存清理完成")
    
    print("\n" + "="*60)
    print("所有示例运行完成！")
    print("="*60)


if __name__ == "__main__":
    main()
