#!/usr/bin/env python3
"""
基础功能测试脚本
测试模型加载和基本特征提取
"""

import torch
import pandas as pd
import numpy as np
from transformers import EsmModel, EsmTokenizer
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_data_loading():
    """测试数据加载"""
    print("1. 测试数据加载...")
    
    try:
        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')
        print(f"   [OK] 成功加载数据，形状: {df.shape}")
        print(f"   列名: {df.columns.tolist()}")
        
        # 检查必需的列
        if 'ID' in df.columns and 'Sequence' in df.columns:
            print("   [OK] 包含必需的列")
            
            # 显示前几个样本
            sample_data = df[['ID', 'Sequence']].head(3)
            for i, row in sample_data.iterrows():
                seq_len = len(row['Sequence']) if pd.notna(row['Sequence']) else 0
                print(f"   样本 {i+1}: ID={row['ID']}, 序列长度={seq_len}")
            
            return True
        else:
            print("   [FAIL] 缺少必需的列")
            return False
            
    except Exception as e:
        print(f"   [FAIL] 数据加载失败: {e}")
        return False

def test_esm_model():
    """测试ESM-2模型加载"""
    print("\n2. 测试ESM-2模型加载...")
    
    try:
        model_name = "facebook/esm2_t33_650M_UR50D"
        print(f"   加载模型: {model_name}")
        
        # 加载tokenizer
        tokenizer = EsmTokenizer.from_pretrained(model_name)
        print("   [OK] Tokenizer加载成功")
        
        # 加载模型
        model = EsmModel.from_pretrained(model_name)
        print("   [OK] 模型加载成功")
        
        # 检查设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        model.eval()
        print(f"   [OK] 模型移动到设备: {device}")
        
        return tokenizer, model, device
        
    except Exception as e:
        print(f"   [FAIL] 模型加载失败: {e}")
        return None, None, None

def test_feature_extraction(tokenizer, model, device):
    """测试特征提取"""
    print("\n3. 测试特征提取...")
    
    if tokenizer is None or model is None:
        print("   [FAIL] 模型未加载，跳过测试")
        return False
    
    try:
        # 测试序列
        test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        print(f"   测试序列长度: {len(test_sequence)}")
        
        # Tokenize
        inputs = tokenizer(test_sequence, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        print("   [OK] 序列tokenization成功")
        
        # 前向传播
        with torch.no_grad():
            outputs = model(**inputs, output_attentions=True, output_hidden_states=True)
        
        print("   [OK] 前向传播成功")
        
        # 检查输出
        last_hidden_state = outputs.last_hidden_state
        print(f"   输出形状: {last_hidden_state.shape}")
        
        # 提取序列级特征（去除CLS和SEP token）
        sequence_features = last_hidden_state[0, 1:-1, :]  # (seq_len, hidden_dim)
        print(f"   序列特征形状: {sequence_features.shape}")
        
        # 计算序列级表示（平均池化）
        pooled_features = sequence_features.mean(dim=0)  # (hidden_dim,)
        print(f"   池化特征形状: {pooled_features.shape}")
        
        # 转换为numpy
        feature_vector = pooled_features.cpu().numpy()
        print(f"   特征向量维度: {feature_vector.shape[0]}")
        print(f"   特征范围: [{feature_vector.min():.4f}, {feature_vector.max():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"   [FAIL] 特征提取失败: {e}")
        return False

def test_batch_processing():
    """测试批量处理逻辑"""
    print("\n4. 测试批量处理逻辑...")
    
    try:
        # 加载数据
        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')
        data = df[['ID', 'Sequence']].head(5)  # 只取前5个样本测试
        
        print(f"   测试数据: {len(data)} 个样本")
        
        # 模拟处理结果
        results = []
        for i, row in data.iterrows():
            protein_id = row['ID']
            sequence = row['Sequence']
            
            if pd.isna(sequence) or len(sequence) == 0:
                print(f"   跳过无效序列: {protein_id}")
                continue
            
            # 模拟特征向量（实际应该是从模型提取）
            mock_features = np.random.randn(1280)  # ESM-2特征维度
            
            result = {
                'ID': protein_id,
                'Sequence': sequence,
                'fusion_vector': ','.join(map(str, mock_features)),
                'vector_dim': len(mock_features),
                'sequence_length': len(sequence)
            }
            
            results.append(result)
            print(f"   处理 {protein_id}: 序列长度={len(sequence)}")
        
        # 转换为DataFrame
        results_df = pd.DataFrame(results)
        print(f"   [OK] 批量处理逻辑测试成功，处理了 {len(results_df)} 个样本")
        
        # 保存测试结果
        test_output_file = "test_results.csv"
        results_df.to_csv(test_output_file, index=False)
        print(f"   [OK] 测试结果保存到: {test_output_file}")
        
        return True
        
    except Exception as e:
        print(f"   [FAIL] 批量处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("基础功能测试")
    print("="*60)
    
    tests = []
    
    # 测试数据加载
    tests.append(test_data_loading())
    
    # 测试模型加载
    tokenizer, model, device = test_esm_model()
    tests.append(tokenizer is not None and model is not None)
    
    # 测试特征提取
    if tokenizer and model:
        tests.append(test_feature_extraction(tokenizer, model, device))
    else:
        tests.append(False)
    
    # 测试批量处理逻辑
    tests.append(test_batch_processing())
    
    # 汇总结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    test_names = [
        "数据加载",
        "ESM-2模型加载", 
        "特征提取",
        "批量处理逻辑"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, tests)):
        status = "[OK] 通过" if result else "[FAIL] 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(tests)} 项测试通过")
    
    if passed == len(tests):
        print("[OK] 所有测试通过，可以开始正式处理")
    elif passed >= len(tests) - 1:
        print("⚠ 大部分测试通过，可以尝试运行")
    else:
        print("[FAIL] 多项测试失败，请检查环境配置")
    
    print("="*60)

if __name__ == "__main__":
    main()
