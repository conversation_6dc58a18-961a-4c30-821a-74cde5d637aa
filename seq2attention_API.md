# seq2attention API 文档

## 概述

`seq2attention.py` 是一个基于 ESM-2 + MSA Transformer 的 architectural 融合特征提取模块，提供了将蛋白质序列转换为高维融合向量的功能。

## 核心功能

### 主要函数

#### `seq2attention(sequence, max_length=1024, device=None)`

将蛋白质序列转换为 architectural 融合向量。

**参数:**
- `sequence` (str | List[str]): 蛋白质序列字符串或序列列表
- `max_length` (int, 可选): 最大序列长度，默认 1024
- `device` (str, 可选): 计算设备 ('cuda', 'cpu', 或 None 自动选择)

**返回值:**
- 单个序列: `numpy.ndarray` (1280维) 或 `None` (失败时)
- 序列列表: `List[numpy.ndarray | None]`

**示例:**
```python
from seq2attention import seq2attention

# 单个序列
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
vector = seq2attention(sequence)
print(f"向量维度: {len(vector)}")  # 输出: 向量维度: 1280

# 多个序列
sequences = ["MKTVRQ...", "AKLMNT..."]
vectors = seq2attention(sequences)
```

### 辅助函数

#### `get_vector_dimension()`
返回融合向量的维度 (1280)。

#### `is_cuda_available()`
检查 CUDA 是否可用。

#### `clear_cache()`
清理模型缓存，释放 GPU 内存。

## 技术特点

### Architectural 融合架构
- **ESM-2 特征**: 单序列深度语言模型表示 (1280维)
- **MSA 特征**: 多序列比对进化信息 (768维)
- **融合机制**: 多头注意力 + 残差连接
- **输出维度**: 1280维融合向量

### 模型配置
- **ESM-2 模型**: facebook/esm2_t33_650M_UR50D
- **MSA 模型**: facebook/esm_msa1b_t12_100M_UR50S
- **融合层**: 8个注意力头，双层全连接网络
- **序列长度**: 支持 10-1024 残基

### 性能特点
- **延迟加载**: 首次调用时初始化模型
- **单例模式**: 全局共享模型实例
- **内存管理**: 自动 GPU 内存清理
- **错误处理**: 完善的异常处理机制

## 使用场景

### 1. 蛋白质功能预测
```python
from seq2attention import seq2attention
import numpy as np

# 提取特征用于分类
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
features = seq2attention(sequence)

# 用于机器学习模型
# model.predict(features.reshape(1, -1))
```

### 2. 序列相似性分析
```python
from seq2attention import seq2attention
import numpy as np

# 计算两个序列的相似性
seq1 = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
seq2 = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"

vec1 = seq2attention(seq1)
vec2 = seq2attention(seq2)

# 余弦相似性
similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
print(f"相似性: {similarity:.4f}")
```

### 3. 批量数据处理
```python
import pandas as pd
from seq2attention import seq2attention

# 处理 DataFrame
df = pd.read_csv('proteins.csv')
vectors = seq2attention(df['sequence'].tolist())

# 添加向量到 DataFrame
df['fusion_vector'] = vectors
df['success'] = df['fusion_vector'].apply(lambda x: x is not None)
```

### 4. 聚类分析
```python
from seq2attention import seq2attention
from sklearn.cluster import KMeans
import numpy as np

# 提取多个序列的特征
sequences = ["seq1", "seq2", "seq3", ...]
vectors = seq2attention(sequences)

# 过滤成功的向量
valid_vectors = [v for v in vectors if v is not None]
feature_matrix = np.array(valid_vectors)

# K-means 聚类
kmeans = KMeans(n_clusters=3)
clusters = kmeans.fit_predict(feature_matrix)
```

## 错误处理

### 常见错误情况
1. **序列过短** (<10 残基): 返回 None，记录警告
2. **序列过长** (>1024 残基): 自动截断
3. **非法字符**: 返回 None，记录错误
4. **空序列**: 返回 None，记录警告
5. **内存不足**: 抛出异常

### 错误处理示例
```python
from seq2attention import seq2attention

def safe_extract_vector(sequence):
    try:
        vector = seq2attention(sequence)
        if vector is not None:
            return vector
        else:
            print(f"序列处理失败: {sequence[:20]}...")
            return None
    except Exception as e:
        print(f"提取向量时出错: {e}")
        return None
```

## 性能优化

### 内存管理
```python
from seq2attention import seq2attention, clear_cache

# 处理大量序列后清理缓存
for i, sequence in enumerate(large_sequence_list):
    vector = seq2attention(sequence)
    # 处理向量...
    
    # 每100个序列清理一次缓存
    if i % 100 == 0:
        clear_cache()
```

### 批量处理
```python
# 推荐：批量处理
sequences = ["seq1", "seq2", "seq3", ...]
vectors = seq2attention(sequences)  # 一次调用处理多个

# 不推荐：逐个处理
# vectors = [seq2attention(seq) for seq in sequences]
```

## 系统要求

### 硬件要求
- **GPU**: 推荐 NVIDIA GPU (8GB+ 显存)
- **内存**: 至少 16GB RAM
- **存储**: 至少 10GB 可用空间

### 软件依赖
- Python 3.8+
- PyTorch 1.9+
- Transformers 4.20+
- ESM (fair-esm)
- NumPy, Pandas

### 环境配置
```bash
# 激活 conda 环境
conda activate attention_fusion

# 验证安装
python -c "from seq2attention import seq2attention; print('✓ 模块导入成功')"
```

## 注意事项

1. **首次使用**: 第一次调用会加载模型，需要较长时间
2. **内存使用**: 模型较大，注意内存和显存使用
3. **序列质量**: 确保输入序列为有效的氨基酸序列
4. **批量处理**: 大量序列建议分批处理，避免内存溢出
5. **模型缓存**: 模型会自动缓存，重复使用时速度更快

## 版本信息

- **版本**: 1.0.0
- **更新日期**: 2025-06-25
- **兼容性**: Python 3.8+, PyTorch 1.9+
- **许可证**: MIT License
