#!/usr/bin/env python3
"""
简化版蛋白质特征融合处理器
仅使用ESM-2特征，快速处理和测试
"""

import pandas as pd
import numpy as np
import torch
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from tqdm import tqdm
import gc

from transformers import EsmModel, EsmTokenizer

# 设置离线模式
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_fusion_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SimpleFusionProcessor:
    """简化版特征融合处理器"""
    
    def __init__(self, 
                 output_dir: str = "simple_output",
                 checkpoint_interval: int = 5):
        """
        初始化处理器
        
        Args:
            output_dir: 输出目录
            checkpoint_interval: 检查点保存间隔
        """
        self.output_dir = output_dir
        self.checkpoint_interval = checkpoint_interval
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化模型
        self._initialize_model()
        
        # 状态跟踪
        self.processed_count = 0
        self.total_count = 0
        self.checkpoint_file = os.path.join(output_dir, "processing_checkpoint.json")
        self.results_file = os.path.join(output_dir, "architectural_fusion_vectors.csv")
        
    def _get_local_model_path(self):
        """获取本地模型路径"""
        base_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D"
        snapshots_path = os.path.join(base_path, "snapshots")
        if os.path.exists(snapshots_path):
            snapshot_dirs = [d for d in os.listdir(snapshots_path) 
                           if os.path.isdir(os.path.join(snapshots_path, d))]
            if snapshot_dirs:
                model_path = os.path.join(snapshots_path, snapshot_dirs[0])
                required_files = ['config.json', 'pytorch_model.bin', 'tokenizer_config.json']
                if all(os.path.exists(os.path.join(model_path, f)) for f in required_files):
                    return model_path
        return None
        
    def _initialize_model(self):
        """初始化ESM-2模型"""
        logger.info("初始化ESM-2模型...")
        
        try:
            # 获取本地模型路径
            local_path = self._get_local_model_path()
            if local_path:
                logger.info(f"使用本地模型: {local_path}")
                self.tokenizer = EsmTokenizer.from_pretrained(local_path, local_files_only=True)
                self.model = EsmModel.from_pretrained(local_path, local_files_only=True)
            else:
                logger.info("使用在线模型")
                model_name = "facebook/esm2_t33_650M_UR50D"
                self.tokenizer = EsmTokenizer.from_pretrained(model_name)
                self.model = EsmModel.from_pretrained(model_name)
            
            # 设置设备
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"✓ 模型加载成功，设备: {self.device}")
            
            # 打印模型信息
            total_params = sum(p.numel() for p in self.model.parameters())
            logger.info(f"模型参数量: {total_params:,}")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise
    
    def load_data(self, excel_file: str) -> pd.DataFrame:
        """加载数据"""
        logger.info(f"加载数据文件: {excel_file}")
        
        try:
            df = pd.read_excel(excel_file, engine='openpyxl')
            logger.info(f"原始数据形状: {df.shape}")
            
            # 提取需要的列
            data = df[['ID', 'Sequence']].copy()
            
            # 清理数据
            data = data.dropna(subset=['ID', 'Sequence'])
            data = data[data['Sequence'].str.len() > 0]
            
            logger.info(f"清理后数据形状: {data.shape}")
            
            return data
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
    
    def load_checkpoint(self) -> Dict:
        """加载检查点"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r') as f:
                    checkpoint = json.load(f)
                logger.info(f"加载检查点: 已处理 {checkpoint.get('processed_count', 0)} 个样本")
                return checkpoint
            except Exception as e:
                logger.warning(f"加载检查点失败: {e}")
        
        return {"processed_count": 0, "processed_ids": []}
    
    def save_checkpoint(self, processed_count: int, processed_ids: List):
        """保存检查点"""
        checkpoint = {
            "processed_count": processed_count,
            "processed_ids": processed_ids,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            with open(self.checkpoint_file, 'w') as f:
                json.dump(checkpoint, f, indent=2)
            logger.info(f"保存检查点: 已处理 {processed_count} 个样本")
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")
    
    def extract_esm_features(self, sequence: str, protein_id: str) -> Optional[np.ndarray]:
        """提取ESM-2特征"""
        try:
            # 检查序列长度
            if len(sequence) > 1024:
                logger.warning(f"序列 {protein_id} 过长 ({len(sequence)} 残基)，跳过处理")
                return None
            
            if len(sequence) < 10:
                logger.warning(f"序列 {protein_id} 过短 ({len(sequence)} 残基)，跳过处理")
                return None
            
            # Tokenize
            inputs = self.tokenizer(sequence, return_tensors="pt", padding=True, truncation=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 前向传播
            with torch.no_grad():
                outputs = self.model(**inputs)
            
            # 提取特征
            last_hidden_state = outputs.last_hidden_state
            
            # 去除特殊token（CLS和SEP）
            sequence_features = last_hidden_state[0, 1:-1, :]  # (seq_len, hidden_dim)
            
            # 序列级池化（平均）
            pooled_features = sequence_features.mean(dim=0)  # (hidden_dim,)
            
            # 转换为numpy
            feature_vector = pooled_features.cpu().numpy()
            
            logger.debug(f"成功提取 {protein_id} 的特征，维度: {feature_vector.shape}")
            
            return feature_vector
            
        except Exception as e:
            logger.error(f"提取 {protein_id} 特征失败: {e}")
            return None
        finally:
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
    
    def process_batch(self, data: pd.DataFrame) -> pd.DataFrame:
        """批量处理"""
        # 加载检查点
        checkpoint = self.load_checkpoint()
        processed_ids = set(checkpoint.get("processed_ids", []))
        start_index = checkpoint.get("processed_count", 0)
        
        # 过滤已处理的数据
        remaining_data = data[~data['ID'].isin(processed_ids)].reset_index(drop=True)
        self.total_count = len(data)
        self.processed_count = start_index
        
        logger.info(f"总样本数: {self.total_count}")
        logger.info(f"已处理: {self.processed_count}")
        logger.info(f"待处理: {len(remaining_data)}")
        
        # 准备结果存储
        results = []
        
        # 如果存在已保存的结果文件，加载它
        if os.path.exists(self.results_file):
            try:
                existing_results = pd.read_csv(self.results_file)
                logger.info(f"加载已有结果文件，包含 {len(existing_results)} 条记录")
            except Exception as e:
                logger.warning(f"加载已有结果文件失败: {e}")
                existing_results = pd.DataFrame()
        else:
            existing_results = pd.DataFrame()
        
        # 处理剩余数据
        for idx, row in tqdm(remaining_data.iterrows(), 
                           total=len(remaining_data), 
                           desc="处理蛋白质特征"):
            
            protein_id = row['ID']
            sequence = row['Sequence']
            
            logger.info(f"处理 {protein_id} (序列长度: {len(sequence)})")
            
            # 提取特征
            feature_vector = self.extract_esm_features(sequence, protein_id)
            
            if feature_vector is not None:
                # 将特征向量转换为字符串
                fusion_vector_str = ','.join(map(str, feature_vector))
                
                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': fusion_vector_str,
                    'vector_dim': len(feature_vector),
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat()
                }
                
                results.append(result_row)
                processed_ids.add(protein_id)
                self.processed_count += 1
                
                logger.info(f"✓ {protein_id} 处理完成，特征维度: {len(feature_vector)}")
                
            else:
                logger.warning(f"✗ {protein_id} 处理失败")
                # 记录失败的样本
                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': None,
                    'vector_dim': None,
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat()
                }
                results.append(result_row)
                processed_ids.add(protein_id)
                self.processed_count += 1
            
            # 定期保存结果和检查点
            if len(results) >= self.checkpoint_interval:
                self._save_intermediate_results(results, existing_results)
                self.save_checkpoint(self.processed_count, list(processed_ids))
                results = []  # 清空临时结果
                
                # 重新加载已有结果
                if os.path.exists(self.results_file):
                    existing_results = pd.read_csv(self.results_file)
        
        # 保存最后的结果
        if results:
            self._save_intermediate_results(results, existing_results)
            self.save_checkpoint(self.processed_count, list(processed_ids))
        
        # 加载最终结果
        final_results = pd.read_csv(self.results_file)
        logger.info(f"处理完成！总共处理了 {len(final_results)} 个样本")
        
        return final_results
    
    def _save_intermediate_results(self, new_results: List[Dict], existing_results: pd.DataFrame):
        """保存中间结果"""
        try:
            # 转换新结果为DataFrame
            new_df = pd.DataFrame(new_results)
            
            # 合并结果
            if not existing_results.empty:
                combined_results = pd.concat([existing_results, new_df], ignore_index=True)
            else:
                combined_results = new_df
            
            # 去重（基于ID）
            combined_results = combined_results.drop_duplicates(subset=['ID'], keep='last')
            
            # 保存到CSV
            combined_results.to_csv(self.results_file, index=False)
            logger.info(f"保存中间结果: {len(combined_results)} 条记录")
            
        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")


def main():
    """主函数"""
    print("="*60)
    print("简化版蛋白质特征融合处理器")
    print("仅使用ESM-2特征")
    print("="*60)
    
    # 检查输入文件
    input_file = "Km_Data.xlsx"
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        return
    
    try:
        # 初始化处理器
        processor = SimpleFusionProcessor(output_dir="simple_output")
        
        # 加载数据
        data = processor.load_data(input_file)
        
        print(f"\n数据概览:")
        print(f"总样本数: {len(data)}")
        
        # 序列长度统计
        seq_lengths = data['Sequence'].str.len()
        print(f"序列长度统计:")
        print(f"  最小: {seq_lengths.min()}")
        print(f"  最大: {seq_lengths.max()}")
        print(f"  平均: {seq_lengths.mean():.1f}")
        print(f"  中位数: {seq_lengths.median():.1f}")
        
        # 检查可处理的序列
        valid_seqs = (seq_lengths >= 10) & (seq_lengths <= 1024)
        print(f"  可处理序列数: {valid_seqs.sum()}")
        print(f"  过长序列 (>1024): {(seq_lengths > 1024).sum()}")
        print(f"  过短序列 (<10): {(seq_lengths < 10).sum()}")
        
        # 询问是否继续
        response = input(f"\n准备处理 {valid_seqs.sum()} 个有效序列，是否继续? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("处理已取消")
            return
        
        # 开始处理
        print(f"\n开始处理...")
        results = processor.process_batch(data)
        
        # 显示结果
        print(f"\n处理完成!")
        successful = results['fusion_vector'].notna().sum()
        failed = results['fusion_vector'].isna().sum()
        
        print(f"成功处理: {successful} 个")
        print(f"处理失败: {failed} 个")
        print(f"成功率: {successful / len(results) * 100:.1f}%")
        
        output_file = os.path.join("simple_output", "architectural_fusion_vectors.csv")
        print(f"\n输出文件: {output_file}")
        
    except KeyboardInterrupt:
        print(f"\n处理被用户中断，进度已保存")
    except Exception as e:
        print(f"\n处理过程中出现错误: {e}")
        logger.error(f"处理错误: {e}", exc_info=True)


if __name__ == "__main__":
    main()
