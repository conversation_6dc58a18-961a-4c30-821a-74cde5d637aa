"""
蛋白质特征融合批处理器
支持从Excel文件读取数据，计算ESM-2和MSA融合特征，支持中断恢复
"""

import pandas as pd
import numpy as np
import torch
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import argparse
from tqdm import tqdm
import pickle
import gc

from feature_extractor import ProteinFeatureExtractor
from evoformer_model import EvoformerModel

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fusion_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ArchitecturalFusionProcessor:
    """架构级融合特征处理器"""
    
    def __init__(self, 
                 esm_model_name: str = "facebook/esm2_t33_650M_UR50D",
                 batch_size: int = 1,
                 output_dir: str = "output",
                 checkpoint_interval: int = 10):
        """
        初始化处理器
        
        Args:
            esm_model_name: ESM-2模型名称
            batch_size: 批处理大小
            output_dir: 输出目录
            checkpoint_interval: 检查点保存间隔
        """
        self.esm_model_name = esm_model_name
        self.batch_size = batch_size
        self.output_dir = output_dir
        self.checkpoint_interval = checkpoint_interval
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化模型
        self._initialize_models()
        
        # 状态跟踪
        self.processed_count = 0
        self.total_count = 0
        self.checkpoint_file = os.path.join(output_dir, "processing_checkpoint.json")
        self.results_file = os.path.join(output_dir, "architectural_fusion_vectors.csv")
        
    def _initialize_models(self):
        """初始化特征提取器和融合模型"""
        logger.info("初始化特征提取器...")
        self.feature_extractor = ProteinFeatureExtractor(esm_model_name=self.esm_model_name)
        
        logger.info("初始化Evoformer融合模型...")
        self.fusion_model = EvoformerModel(
            esm_dim=1280,      # ESM-2 t33模型的特征维度
            msa_dim=256,       # MSA特征维度
            pair_dim=128,      # Pair特征维度
            num_blocks=4,      # Evoformer块数量
            num_heads=8,       # 注意力头数
            dropout=0.1        # Dropout率
        )
        
        # 设置为评估模式
        self.fusion_model.eval()
        
        # 打印模型信息
        total_params = sum(p.numel() for p in self.fusion_model.parameters())
        logger.info(f"融合模型总参数量: {total_params:,}")
        
    def load_data(self, excel_file: str) -> pd.DataFrame:
        """
        从Excel文件加载数据
        
        Args:
            excel_file: Excel文件路径
            
        Returns:
            包含ID和Sequence列的DataFrame
        """
        logger.info(f"加载数据文件: {excel_file}")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file, engine='openpyxl')
            logger.info(f"原始数据形状: {df.shape}")
            logger.info(f"列名: {df.columns.tolist()}")
            
            # 检查必需的列
            required_columns = ['ID', 'Sequence']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必需的列: {missing_columns}")
            
            # 提取需要的列
            data = df[['ID', 'Sequence']].copy()
            
            # 清理数据
            data = data.dropna(subset=['ID', 'Sequence'])
            data = data[data['Sequence'].str.len() > 0]  # 移除空序列
            
            logger.info(f"清理后数据形状: {data.shape}")
            
            return data
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
    
    def load_checkpoint(self) -> Dict:
        """加载检查点"""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r') as f:
                    checkpoint = json.load(f)
                logger.info(f"加载检查点: 已处理 {checkpoint.get('processed_count', 0)} 个样本")
                return checkpoint
            except Exception as e:
                logger.warning(f"加载检查点失败: {e}")
        
        return {"processed_count": 0, "processed_ids": []}
    
    def save_checkpoint(self, processed_count: int, processed_ids: List):
        """保存检查点"""
        checkpoint = {
            "processed_count": processed_count,
            "processed_ids": processed_ids,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            with open(self.checkpoint_file, 'w') as f:
                json.dump(checkpoint, f, indent=2)
            logger.info(f"保存检查点: 已处理 {processed_count} 个样本")
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")
    
    def extract_fusion_features(self, sequence: str, protein_id: str) -> Optional[np.ndarray]:
        """
        提取单个蛋白质的融合特征
        
        Args:
            sequence: 蛋白质序列
            protein_id: 蛋白质ID
            
        Returns:
            融合特征向量或None（如果失败）
        """
        try:
            # 检查序列长度（调整限制以适应更多序列）
            if len(sequence) > 1024:  # ESM-2的最大序列长度
                logger.warning(f"序列 {protein_id} 过长 ({len(sequence)} 残基)，跳过处理")
                return None

            if len(sequence) < 10:  # 过短的序列也跳过
                logger.warning(f"序列 {protein_id} 过短 ({len(sequence)} 残基)，跳过处理")
                return None
            
            # 提取和融合特征
            with torch.no_grad():
                results = self.fusion_model.extract_and_fuse(
                    sequence, 
                    self.feature_extractor,
                    msa_database_path=None  # 使用默认MSA生成
                )
                
                # 获取融合特征
                fused_features = results["fused_features"]  # (1, seq_len, feature_dim)
                
                # 序列级特征：对所有残基取平均
                sequence_features = fused_features.mean(dim=1).squeeze(0)  # (feature_dim,)
                
                # 转换为numpy数组
                fusion_vector = sequence_features.cpu().numpy()
                
                logger.debug(f"成功提取 {protein_id} 的融合特征，维度: {fusion_vector.shape}")
                
                return fusion_vector
                
        except Exception as e:
            logger.error(f"提取 {protein_id} 特征失败: {e}")
            return None
        finally:
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()

    def process_batch(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        批量处理蛋白质数据

        Args:
            data: 包含ID和Sequence的DataFrame

        Returns:
            包含融合特征的DataFrame
        """
        # 加载检查点
        checkpoint = self.load_checkpoint()
        processed_ids = set(checkpoint.get("processed_ids", []))
        start_index = checkpoint.get("processed_count", 0)

        # 过滤已处理的数据
        remaining_data = data[~data['ID'].isin(processed_ids)].reset_index(drop=True)
        self.total_count = len(data)
        self.processed_count = start_index

        logger.info(f"总样本数: {self.total_count}")
        logger.info(f"已处理: {self.processed_count}")
        logger.info(f"待处理: {len(remaining_data)}")

        # 准备结果存储
        results = []

        # 如果存在已保存的结果文件，加载它
        if os.path.exists(self.results_file):
            try:
                existing_results = pd.read_csv(self.results_file)
                logger.info(f"加载已有结果文件，包含 {len(existing_results)} 条记录")
            except Exception as e:
                logger.warning(f"加载已有结果文件失败: {e}")
                existing_results = pd.DataFrame()
        else:
            existing_results = pd.DataFrame()

        # 处理剩余数据
        for idx, row in tqdm(remaining_data.iterrows(),
                           total=len(remaining_data),
                           desc="处理蛋白质特征"):

            protein_id = row['ID']
            sequence = row['Sequence']

            logger.info(f"处理 {protein_id} (序列长度: {len(sequence)})")

            # 提取融合特征
            fusion_vector = self.extract_fusion_features(sequence, protein_id)

            if fusion_vector is not None:
                # 将特征向量转换为字符串（用逗号分隔）
                fusion_vector_str = ','.join(map(str, fusion_vector))

                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': fusion_vector_str,
                    'vector_dim': len(fusion_vector),
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat()
                }

                results.append(result_row)
                processed_ids.add(protein_id)
                self.processed_count += 1

                logger.info(f"✓ {protein_id} 处理完成，特征维度: {len(fusion_vector)}")

            else:
                logger.warning(f"✗ {protein_id} 处理失败")
                # 记录失败的样本
                result_row = {
                    'ID': protein_id,
                    'Sequence': sequence,
                    'fusion_vector': None,
                    'vector_dim': None,
                    'sequence_length': len(sequence),
                    'processed_time': datetime.now().isoformat()
                }
                results.append(result_row)
                processed_ids.add(protein_id)
                self.processed_count += 1

            # 定期保存结果和检查点
            if len(results) >= self.checkpoint_interval:
                self._save_intermediate_results(results, existing_results)
                self.save_checkpoint(self.processed_count, list(processed_ids))
                results = []  # 清空临时结果

                # 重新加载已有结果
                if os.path.exists(self.results_file):
                    existing_results = pd.read_csv(self.results_file)

        # 保存最后的结果
        if results:
            self._save_intermediate_results(results, existing_results)
            self.save_checkpoint(self.processed_count, list(processed_ids))

        # 加载最终结果
        final_results = pd.read_csv(self.results_file)
        logger.info(f"处理完成！总共处理了 {len(final_results)} 个样本")

        return final_results

    def _save_intermediate_results(self, new_results: List[Dict], existing_results: pd.DataFrame):
        """保存中间结果"""
        try:
            # 转换新结果为DataFrame
            new_df = pd.DataFrame(new_results)

            # 合并结果
            if not existing_results.empty:
                combined_results = pd.concat([existing_results, new_df], ignore_index=True)
            else:
                combined_results = new_df

            # 去重（基于ID）
            combined_results = combined_results.drop_duplicates(subset=['ID'], keep='last')

            # 保存到CSV
            combined_results.to_csv(self.results_file, index=False)
            logger.info(f"保存中间结果: {len(combined_results)} 条记录")

        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")

    def get_processing_stats(self) -> Dict:
        """获取处理统计信息"""
        stats = {
            "total_samples": self.total_count,
            "processed_samples": self.processed_count,
            "remaining_samples": self.total_count - self.processed_count,
            "progress_percentage": (self.processed_count / self.total_count * 100) if self.total_count > 0 else 0
        }

        if os.path.exists(self.results_file):
            try:
                results_df = pd.read_csv(self.results_file)
                successful_count = results_df['fusion_vector'].notna().sum()
                failed_count = results_df['fusion_vector'].isna().sum()

                stats.update({
                    "successful_extractions": int(successful_count),
                    "failed_extractions": int(failed_count),
                    "success_rate": (successful_count / len(results_df) * 100) if len(results_df) > 0 else 0
                })
            except Exception as e:
                logger.warning(f"获取统计信息失败: {e}")

        return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="蛋白质架构级融合特征提取器")
    parser.add_argument("--input", "-i", required=True, help="输入Excel文件路径")
    parser.add_argument("--output", "-o", default="output", help="输出目录")
    parser.add_argument("--batch-size", "-b", type=int, default=1, help="批处理大小")
    parser.add_argument("--checkpoint-interval", "-c", type=int, default=10, help="检查点保存间隔")
    parser.add_argument("--esm-model", default="facebook/esm2_t33_650M_UR50D", help="ESM-2模型名称")
    parser.add_argument("--resume", action="store_true", help="从检查点恢复处理")
    parser.add_argument("--stats", action="store_true", help="仅显示处理统计信息")

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        logger.error(f"输入文件不存在: {args.input}")
        return

    # 初始化处理器
    processor = ArchitecturalFusionProcessor(
        esm_model_name=args.esm_model,
        batch_size=args.batch_size,
        output_dir=args.output,
        checkpoint_interval=args.checkpoint_interval
    )

    # 如果只是查看统计信息
    if args.stats:
        stats = processor.get_processing_stats()
        print("\n" + "="*50)
        print("处理统计信息")
        print("="*50)
        for key, value in stats.items():
            print(f"{key}: {value}")
        return

    try:
        # 加载数据
        data = processor.load_data(args.input)

        # 显示数据信息
        print(f"\n数据概览:")
        print(f"总样本数: {len(data)}")
        print(f"序列长度统计:")
        seq_lengths = data['Sequence'].str.len()
        print(f"  最小长度: {seq_lengths.min()}")
        print(f"  最大长度: {seq_lengths.max()}")
        print(f"  平均长度: {seq_lengths.mean():.1f}")
        print(f"  中位数长度: {seq_lengths.median():.1f}")

        # 检查是否有超长序列
        long_sequences = seq_lengths > 1000
        if long_sequences.any():
            print(f"  超长序列 (>1000): {long_sequences.sum()} 个")

        # 开始处理
        print(f"\n开始处理...")
        print(f"输出目录: {args.output}")
        print(f"检查点间隔: {args.checkpoint_interval}")

        if args.resume:
            print("从检查点恢复处理...")

        # 批量处理
        results = processor.process_batch(data)

        # 显示最终统计
        stats = processor.get_processing_stats()
        print(f"\n" + "="*50)
        print("处理完成！")
        print("="*50)
        for key, value in stats.items():
            print(f"{key}: {value}")

        # 显示输出文件信息
        output_file = os.path.join(args.output, "architectural_fusion_vectors.csv")
        if os.path.exists(output_file):
            print(f"\n输出文件: {output_file}")
            print(f"文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")

        print(f"\n处理日志: fusion_processing.log")

    except KeyboardInterrupt:
        logger.info("处理被用户中断")
        print("\n处理已中断，进度已保存到检查点")
        print("使用 --resume 参数可以继续处理")

    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        print(f"\n错误: {e}")
        print("请检查日志文件获取详细信息")


def test_single_protein():
    """测试单个蛋白质的处理"""
    print("测试单个蛋白质特征提取...")

    # 测试序列
    test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    test_id = "TEST_001"

    # 初始化处理器
    processor = ArchitecturalFusionProcessor(output_dir="test_output")

    # 提取特征
    fusion_vector = processor.extract_fusion_features(test_sequence, test_id)

    if fusion_vector is not None:
        print(f"✓ 测试成功！")
        print(f"  序列长度: {len(test_sequence)}")
        print(f"  特征维度: {fusion_vector.shape}")
        print(f"  特征范围: [{fusion_vector.min():.4f}, {fusion_vector.max():.4f}]")
        print(f"  特征均值: {fusion_vector.mean():.4f}")
        print(f"  特征标准差: {fusion_vector.std():.4f}")
    else:
        print("✗ 测试失败")


if __name__ == "__main__":
    import sys

    if len(sys.argv) == 1:
        # 如果没有参数，运行测试
        test_single_protein()
    else:
        # 运行主程序
        main()
