[{"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "pickle", "kind": 6, "isExtraImport": true, "importPath": "pickle", "description": "pickle", "detail": "pickle", "documentation": {}}, {"label": "gc", "kind": 6, "isExtraImport": true, "importPath": "gc", "description": "gc", "detail": "gc", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "EvoformerModel", "importPath": "evoformer_model", "description": "evoformer_model", "isExtraImport": true, "detail": "evoformer_model", "documentation": {}}, {"label": "EvoformerModel", "importPath": "evoformer_model", "description": "evoformer_model", "isExtraImport": true, "detail": "evoformer_model", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "importlib", "kind": 6, "isExtraImport": true, "importPath": "importlib", "description": "importlib", "detail": "importlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "torch.nn", "kind": 6, "isExtraImport": true, "importPath": "torch.nn", "description": "torch.nn", "detail": "torch.nn", "documentation": {}}, {"label": "torch.nn.functional", "kind": 6, "isExtraImport": true, "importPath": "torch.nn.functional", "description": "torch.nn.functional", "detail": "torch.nn.functional", "documentation": {}}, {"label": "math", "kind": 6, "isExtraImport": true, "importPath": "math", "description": "math", "detail": "math", "documentation": {}}, {"label": "rearrange", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "repeat", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "visualize_pair_representation", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_contact_map", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_msa_representation", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "plot_representation_evolution", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "print_evoformer_summary", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "analyze_evoformer_outputs", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "esm", "kind": 6, "isExtraImport": true, "importPath": "esm", "description": "esm", "detail": "esm", "documentation": {}}, {"label": "ArchitecturalFusionProcessor", "importPath": "architectural_fusion_processor", "description": "architectural_fusion_processor", "isExtraImport": true, "detail": "architectural_fusion_processor", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "seaborn", "kind": 6, "isExtraImport": true, "importPath": "seaborn", "description": "seaborn", "detail": "seaborn", "documentation": {}}, {"label": "ArchitecturalFusionProcessor", "kind": 6, "importPath": "architectural_fusion_processor", "description": "architectural_fusion_processor", "peekOfCode": "class ArchitecturalFusionProcessor:\n    \"\"\"架构级融合特征处理器\"\"\"\n    def __init__(self, \n                 esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\",\n                 batch_size: int = 1,\n                 output_dir: str = \"output\",\n                 checkpoint_interval: int = 10):\n        \"\"\"\n        初始化处理器\n        Args:", "detail": "architectural_fusion_processor", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "architectural_fusion_processor", "description": "architectural_fusion_processor", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    parser = argparse.ArgumentParser(description=\"蛋白质架构级融合特征提取器\")\n    parser.add_argument(\"--input\", \"-i\", required=True, help=\"输入Excel文件路径\")\n    parser.add_argument(\"--output\", \"-o\", default=\"output\", help=\"输出目录\")\n    parser.add_argument(\"--batch-size\", \"-b\", type=int, default=1, help=\"批处理大小\")\n    parser.add_argument(\"--checkpoint-interval\", \"-c\", type=int, default=10, help=\"检查点保存间隔\")\n    parser.add_argument(\"--esm-model\", default=\"facebook/esm2_t33_650M_UR50D\", help=\"ESM-2模型名称\")\n    parser.add_argument(\"--resume\", action=\"store_true\", help=\"从检查点恢复处理\")\n    parser.add_argument(\"--stats\", action=\"store_true\", help=\"仅显示处理统计信息\")", "detail": "architectural_fusion_processor", "documentation": {}}, {"label": "test_single_protein", "kind": 2, "importPath": "architectural_fusion_processor", "description": "architectural_fusion_processor", "peekOfCode": "def test_single_protein():\n    \"\"\"测试单个蛋白质的处理\"\"\"\n    print(\"测试单个蛋白质特征提取...\")\n    # 测试序列\n    test_sequence = \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\"\n    test_id = \"TEST_001\"\n    # 初始化处理器\n    processor = ArchitecturalFusionProcessor(output_dir=\"test_output\")\n    # 提取特征\n    fusion_vector = processor.extract_fusion_features(test_sequence, test_id)", "detail": "architectural_fusion_processor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "architectural_fusion_processor", "description": "architectural_fusion_processor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass ArchitecturalFusionProcessor:\n    \"\"\"架构级融合特征处理器\"\"\"\n    def __init__(self, \n                 esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\",\n                 batch_size: int = 1,\n                 output_dir: str = \"output\",\n                 checkpoint_interval: int = 10):\n        \"\"\"\n        初始化处理器", "detail": "architectural_fusion_processor", "documentation": {}}, {"label": "check_python_version", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def check_python_version():\n    \"\"\"检查Python版本\"\"\"\n    print(\"1. Python版本检查\")\n    version = sys.version_info\n    print(f\"   当前Python版本: {version.major}.{version.minor}.{version.micro}\")\n    if version.major >= 3 and version.minor >= 8:\n        print(\"   ✓ Python版本符合要求 (>=3.8)\")\n        return True\n    else:\n        print(\"   ✗ Python版本过低，需要Python 3.8或更高版本\")", "detail": "check_environment", "documentation": {}}, {"label": "check_conda_environment", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def check_conda_environment():\n    \"\"\"检查conda环境\"\"\"\n    print(\"\\n2. Conda环境检查\")\n    # 检查是否在conda环境中\n    conda_env = os.environ.get('CONDA_DEFAULT_ENV')\n    if conda_env:\n        print(f\"   当前conda环境: {conda_env}\")\n        if conda_env == 'attention_fusion':\n            print(\"   ✓ 正在使用attention_fusion环境\")\n            return True", "detail": "check_environment", "documentation": {}}, {"label": "check_required_packages", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def check_required_packages():\n    \"\"\"检查必需的Python包\"\"\"\n    print(\"\\n3. Python包依赖检查\")\n    required_packages = {\n        'torch': 'PyTorch',\n        'transformers': 'Hugging Face Transformers',\n        'pandas': 'Pandas',\n        'numpy': 'NumPy',\n        'openpyxl': 'OpenPyXL',\n        'tqdm': 'tqdm',", "detail": "check_environment", "documentation": {}}, {"label": "check_gpu_availability", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def check_gpu_availability():\n    \"\"\"检查GPU可用性\"\"\"\n    print(\"\\n4. GPU检查\")\n    try:\n        import torch\n        if torch.cuda.is_available():\n            gpu_count = torch.cuda.device_count()\n            current_device = torch.cuda.current_device()\n            gpu_name = torch.cuda.get_device_name(current_device)\n            print(f\"   ✓ CUDA可用\")", "detail": "check_environment", "documentation": {}}, {"label": "check_model_cache", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def check_model_cache():\n    \"\"\"检查模型缓存\"\"\"\n    print(\"\\n5. 模型缓存检查\")\n    # 检查Hugging Face缓存\n    hf_cache_dir = Path.home() / '.cache' / 'huggingface'\n    if hf_cache_dir.exists():\n        print(f\"   ✓ Hugging Face缓存目录存在: {hf_cache_dir}\")\n        # 检查ESM-2模型\n        esm_model_path = hf_cache_dir / 'transformers'\n        if esm_model_path.exists():", "detail": "check_environment", "documentation": {}}, {"label": "check_input_file", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def check_input_file():\n    \"\"\"检查输入文件\"\"\"\n    print(\"\\n6. 输入文件检查\")\n    input_file = \"Km_Data.xlsx\"\n    if os.path.exists(input_file):\n        file_size = os.path.getsize(input_file) / 1024 / 1024  # MB\n        print(f\"   ✓ 找到输入文件: {input_file}\")\n        print(f\"   文件大小: {file_size:.2f} MB\")\n        # 尝试读取文件\n        try:", "detail": "check_environment", "documentation": {}}, {"label": "check_disk_space", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def check_disk_space():\n    \"\"\"检查磁盘空间\"\"\"\n    print(\"\\n7. 磁盘空间检查\")\n    try:\n        import shutil\n        total, used, free = shutil.disk_usage(\".\")\n        free_gb = free / 1024**3\n        print(f\"   可用磁盘空间: {free_gb:.2f} GB\")\n        if free_gb > 10:\n            print(\"   ✓ 磁盘空间充足\")", "detail": "check_environment", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "check_environment", "description": "check_environment", "peekOfCode": "def main():\n    \"\"\"主检查函数\"\"\"\n    print(\"=\"*60)\n    print(\"蛋白质特征融合环境检查\")\n    print(\"=\"*60)\n    checks = [\n        check_python_version(),\n        check_conda_environment(),\n        check_required_packages(),\n        check_gpu_availability(),", "detail": "check_environment", "documentation": {}}, {"label": "OuterProductMean", "kind": 6, "importPath": "evoformer_model", "description": "evoformer_model", "peekOfCode": "class OuterProductMean(nn.Module):\n    \"\"\"外积均值模块，用于从MSA更新Pair representation\"\"\"\n    def __init__(self, msa_dim: int, pair_dim: int, hidden_dim: int = 32):\n        \"\"\"\n        初始化外积均值模块\n        Args:\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度\n            hidden_dim: 隐藏层维度\n        \"\"\"", "detail": "evoformer_model", "documentation": {}}, {"label": "TriangleMultiplication", "kind": 6, "importPath": "evoformer_model", "description": "evoformer_model", "peekOfCode": "class TriangleMultiplication(nn.Module):\n    \"\"\"三角乘法模块，用于Pair representation的自更新\"\"\"\n    def __init__(self, pair_dim: int, hidden_dim: int = 128):\n        \"\"\"\n        初始化三角乘法模块\n        Args:\n            pair_dim: Pair特征维度\n            hidden_dim: 隐藏层维度\n        \"\"\"\n        super().__init__()", "detail": "evoformer_model", "documentation": {}}, {"label": "TriangleAttention", "kind": 6, "importPath": "evoformer_model", "description": "evoformer_model", "peekOfCode": "class TriangleAttention(nn.Module):\n    \"\"\"三角注意力模块\"\"\"\n    def __init__(self, pair_dim: int, num_heads: int = 4, dropout: float = 0.1):\n        \"\"\"\n        初始化三角注意力模块\n        Args:\n            pair_dim: Pair特征维度\n            num_heads: 注意力头数\n            dropout: Dropout率\n        \"\"\"", "detail": "evoformer_model", "documentation": {}}, {"label": "MSARowAttention", "kind": 6, "importPath": "evoformer_model", "description": "evoformer_model", "peekOfCode": "class MSARowAttention(nn.Module):\n    \"\"\"MSA行注意力模块\"\"\"\n    def __init__(self, msa_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化MSA行注意力模块\n        Args:\n            msa_dim: MSA特征维度\n            num_heads: 注意力头数\n            dropout: Dropout率\n        \"\"\"", "detail": "evoformer_model", "documentation": {}}, {"label": "EvoformerBlock", "kind": 6, "importPath": "evoformer_model", "description": "evoformer_model", "peekOfCode": "class EvoformerBlock(nn.Module):\n    \"\"\"Evoformer块，包含MSA和Pair的更新\"\"\"\n    def __init__(self, msa_dim: int, pair_dim: int, single_dim: int,\n                 num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化Evoformer块\n        Args:\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度\n            single_dim: Single特征维度", "detail": "evoformer_model", "documentation": {}}, {"label": "EvoformerModel", "kind": 6, "importPath": "evoformer_model", "description": "evoformer_model", "peekOfCode": "class EvoformerModel(nn.Module):\n    \"\"\"Evoformer模型主类，实现架构级融合\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, pair_dim: int = 128,\n                 num_blocks: int = 4, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化Evoformer模型\n        Args:\n            esm_dim: ESM-2特征维度 (用作single representation)\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度", "detail": "evoformer_model", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "evoformer_model", "description": "evoformer_model", "peekOfCode": "logger = logging.getLogger(__name__)\nclass OuterProductMean(nn.Module):\n    \"\"\"外积均值模块，用于从MSA更新Pair representation\"\"\"\n    def __init__(self, msa_dim: int, pair_dim: int, hidden_dim: int = 32):\n        \"\"\"\n        初始化外积均值模块\n        Args:\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度\n            hidden_dim: 隐藏层维度", "detail": "evoformer_model", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "example", "description": "example", "peekOfCode": "def main():\n    \"\"\"主函数，演示架构级融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"架构级融合蛋白质特征提取模型示例 (Evoformer风格)\")\n    print(\"=\" * 80)", "detail": "example", "documentation": {}}, {"label": "demonstrate_evoformer_components", "kind": 2, "importPath": "example", "description": "example", "peekOfCode": "def demonstrate_evoformer_components(model):\n    \"\"\"演示Evoformer组件的工作原理\"\"\"\n    print(\"\\n1. Evoformer组件测试...\")\n    # 创建测试数据\n    batch_size, seq_len, num_sequences = 1, 50, 32\n    esm_dim, msa_dim, pair_dim = 1280, 256, 128\n    # 模拟输入数据\n    single_repr = torch.randn(batch_size, seq_len, esm_dim)\n    msa_repr = torch.randn(batch_size, num_sequences, seq_len, 21)  # 21 = 20 AA + gap\n    pair_repr = torch.randn(batch_size, seq_len, seq_len, 128)  # 初始pair representation", "detail": "example", "documentation": {}}, {"label": "compare_architectural_fusion", "kind": 2, "importPath": "example", "description": "example", "peekOfCode": "def compare_architectural_fusion():\n    \"\"\"比较架构级融合与其他方法的优势\"\"\"\n    print(\"\\n\" + \"=\"*60)\n    print(\"架构级融合方法比较\")\n    print(\"=\"*60)\n    print(\"Evoformer架构级融合的优势:\")\n    print(\"  ✓ 双流网络设计，充分利用不同特征的特性\")\n    print(\"  ✓ 多层信息交换，实现深度特征融合\")\n    print(\"  ✓ 三角注意力和三角乘法，捕获复杂的残基对关系\")\n    print(\"  ✓ 外积均值操作，有效整合MSA信息到Pair表示\")", "detail": "example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "example", "description": "example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"主函数，演示架构级融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"架构级融合蛋白质特征提取模型示例 (Evoformer风格)\")", "detail": "example", "documentation": {}}, {"label": "EvoformerFeatureExtractor", "kind": 6, "importPath": "feature_extractor", "description": "feature_extractor", "peekOfCode": "class EvoformerFeatureExtractor:\n    \"\"\"Evoformer风格的特征提取器\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n        \"\"\"\n        self.esm_model_name = esm_model_name\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")", "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "feature_extractor", "description": "feature_extractor", "peekOfCode": "class ProteinFeatureExtractor(EvoformerFeatureExtractor):\n    \"\"\"蛋白质特征提取器的别名，保持接口一致性\"\"\"\n    pass", "detail": "feature_extractor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "feature_extractor", "description": "feature_extractor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass EvoformerFeatureExtractor:\n    \"\"\"Evoformer风格的特征提取器\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n        \"\"\"\n        self.esm_model_name = esm_model_name", "detail": "feature_extractor", "documentation": {}}, {"label": "ESM_MSA_FusionModel", "kind": 6, "importPath": "incremental_fusion_processor", "description": "incremental_fusion_processor", "peekOfCode": "class ESM_MSA_FusionModel(nn.Module):\n    \"\"\"ESM-2 + MSA特征融合模型\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):\n        super().__init__()\n        self.esm_dim = esm_dim\n        self.msa_dim = msa_dim\n        self.fusion_dim = fusion_dim\n        # ESM-2特征投影\n        self.esm_projection = nn.Sequential(\n            nn.Linear(esm_dim, fusion_dim),", "detail": "incremental_fusion_processor", "documentation": {}}, {"label": "IncrementalFusionProcessor", "kind": 6, "importPath": "incremental_fusion_processor", "description": "incremental_fusion_processor", "peekOfCode": "class IncrementalFusionProcessor:\n    \"\"\"增量式融合处理器 - 每处理一个就保存一个\"\"\"\n    def __init__(self, \n                 output_dir: str = \".\",\n                 max_sequence_length: int = 1024):\n        \"\"\"\n        初始化处理器\n        Args:\n            output_dir: 输出目录\n            max_sequence_length: 最大序列长度", "detail": "incremental_fusion_processor", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "incremental_fusion_processor", "description": "incremental_fusion_processor", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\"*80)\n    print(\"增量式ESM-2 + MSA特征融合处理器\")\n    print(\"每处理一个样本就立即保存，防止数据丢失\")\n    print(\"=\"*80)\n    # 检查输入文件\n    input_file = \"Km_Data.xlsx\"\n    if not os.path.exists(input_file):\n        print(f\"错误: 找不到输入文件 {input_file}\")", "detail": "incremental_fusion_processor", "documentation": {}}, {"label": "os.environ['TRANSFORMERS_OFFLINE']", "kind": 5, "importPath": "incremental_fusion_processor", "description": "incremental_fusion_processor", "peekOfCode": "os.environ['TRANSFORMERS_OFFLINE'] = '1'\nos.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('incremental_fusion_processing.log'),\n        logging.StreamHandler()", "detail": "incremental_fusion_processor", "documentation": {}}, {"label": "os.environ['HF_DATASETS_OFFLINE']", "kind": 5, "importPath": "incremental_fusion_processor", "description": "incremental_fusion_processor", "peekOfCode": "os.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('incremental_fusion_processing.log'),\n        logging.StreamHandler()\n    ]", "detail": "incremental_fusion_processor", "documentation": {}}, {"label": "os.environ['HF_HUB_OFFLINE']", "kind": 5, "importPath": "incremental_fusion_processor", "description": "incremental_fusion_processor", "peekOfCode": "os.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('incremental_fusion_processing.log'),\n        logging.StreamHandler()\n    ]\n)", "detail": "incremental_fusion_processor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "incremental_fusion_processor", "description": "incremental_fusion_processor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass ESM_MSA_FusionModel(nn.Module):\n    \"\"\"ESM-2 + MSA特征融合模型\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):\n        super().__init__()\n        self.esm_dim = esm_dim\n        self.msa_dim = msa_dim\n        self.fusion_dim = fusion_dim\n        # ESM-2特征投影\n        self.esm_projection = nn.Sequential(", "detail": "incremental_fusion_processor", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "run_fusion", "description": "run_fusion", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\"*80)\n    print(\"蛋白质架构级融合特征提取器\")\n    print(\"ESM-2 + MSA Transformer 融合\")\n    print(\"=\"*80)\n    # 检查输入文件\n    input_file = \"Km_Data.xlsx\"\n    if not os.path.exists(input_file):\n        print(f\"错误: 找不到输入文件 {input_file}\")", "detail": "run_fusion", "documentation": {}}, {"label": "show_progress", "kind": 2, "importPath": "run_fusion", "description": "run_fusion", "peekOfCode": "def show_progress():\n    \"\"\"显示当前处理进度\"\"\"\n    output_dir = \"fusion_output\"\n    processor = ArchitecturalFusionProcessor(output_dir=output_dir)\n    try:\n        stats = processor.get_processing_stats()\n        print(\"当前处理进度:\")\n        for key, value in stats.items():\n            if isinstance(value, float):\n                print(f\"  {key}: {value:.2f}\")", "detail": "run_fusion", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "run_fusion", "description": "run_fusion", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"主函数\"\"\"\n    print(\"=\"*80)\n    print(\"蛋白质架构级融合特征提取器\")\n    print(\"ESM-2 + MSA Transformer 融合\")\n    print(\"=\"*80)\n    # 检查输入文件\n    input_file = \"Km_Data.xlsx\"\n    if not os.path.exists(input_file):", "detail": "run_fusion", "documentation": {}}, {"label": "SimpleFusionProcessor", "kind": 6, "importPath": "simple_fusion_processor", "description": "simple_fusion_processor", "peekOfCode": "class SimpleFusionProcessor:\n    \"\"\"简化版特征融合处理器\"\"\"\n    def __init__(self, \n                 output_dir: str = \"simple_output\",\n                 checkpoint_interval: int = 5):\n        \"\"\"\n        初始化处理器\n        Args:\n            output_dir: 输出目录\n            checkpoint_interval: 检查点保存间隔", "detail": "simple_fusion_processor", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "simple_fusion_processor", "description": "simple_fusion_processor", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\"*60)\n    print(\"简化版蛋白质特征融合处理器\")\n    print(\"仅使用ESM-2特征\")\n    print(\"=\"*60)\n    # 检查输入文件\n    input_file = \"Km_Data.xlsx\"\n    if not os.path.exists(input_file):\n        print(f\"错误: 找不到输入文件 {input_file}\")", "detail": "simple_fusion_processor", "documentation": {}}, {"label": "os.environ['TRANSFORMERS_OFFLINE']", "kind": 5, "importPath": "simple_fusion_processor", "description": "simple_fusion_processor", "peekOfCode": "os.environ['TRANSFORMERS_OFFLINE'] = '1'\nos.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('simple_fusion_processing.log'),\n        logging.StreamHandler()", "detail": "simple_fusion_processor", "documentation": {}}, {"label": "os.environ['HF_DATASETS_OFFLINE']", "kind": 5, "importPath": "simple_fusion_processor", "description": "simple_fusion_processor", "peekOfCode": "os.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('simple_fusion_processing.log'),\n        logging.StreamHandler()\n    ]", "detail": "simple_fusion_processor", "documentation": {}}, {"label": "os.environ['HF_HUB_OFFLINE']", "kind": 5, "importPath": "simple_fusion_processor", "description": "simple_fusion_processor", "peekOfCode": "os.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('simple_fusion_processing.log'),\n        logging.StreamHandler()\n    ]\n)", "detail": "simple_fusion_processor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "simple_fusion_processor", "description": "simple_fusion_processor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass SimpleFusionProcessor:\n    \"\"\"简化版特征融合处理器\"\"\"\n    def __init__(self, \n                 output_dir: str = \"simple_output\",\n                 checkpoint_interval: int = 5):\n        \"\"\"\n        初始化处理器\n        Args:\n            output_dir: 输出目录", "detail": "simple_fusion_processor", "documentation": {}}, {"label": "test_data_loading", "kind": 2, "importPath": "test_basic", "description": "test_basic", "peekOfCode": "def test_data_loading():\n    \"\"\"测试数据加载\"\"\"\n    print(\"1. 测试数据加载...\")\n    try:\n        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')\n        print(f\"   ✓ 成功加载数据，形状: {df.shape}\")\n        print(f\"   列名: {df.columns.tolist()}\")\n        # 检查必需的列\n        if 'ID' in df.columns and 'Sequence' in df.columns:\n            print(\"   ✓ 包含必需的列\")", "detail": "test_basic", "documentation": {}}, {"label": "test_esm_model", "kind": 2, "importPath": "test_basic", "description": "test_basic", "peekOfCode": "def test_esm_model():\n    \"\"\"测试ESM-2模型加载\"\"\"\n    print(\"\\n2. 测试ESM-2模型加载...\")\n    try:\n        model_name = \"facebook/esm2_t33_650M_UR50D\"\n        print(f\"   加载模型: {model_name}\")\n        # 加载tokenizer\n        tokenizer = EsmTokenizer.from_pretrained(model_name)\n        print(\"   ✓ Tokenizer加载成功\")\n        # 加载模型", "detail": "test_basic", "documentation": {}}, {"label": "test_feature_extraction", "kind": 2, "importPath": "test_basic", "description": "test_basic", "peekOfCode": "def test_feature_extraction(tokenizer, model, device):\n    \"\"\"测试特征提取\"\"\"\n    print(\"\\n3. 测试特征提取...\")\n    if tokenizer is None or model is None:\n        print(\"   ✗ 模型未加载，跳过测试\")\n        return False\n    try:\n        # 测试序列\n        test_sequence = \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\"\n        print(f\"   测试序列长度: {len(test_sequence)}\")", "detail": "test_basic", "documentation": {}}, {"label": "test_batch_processing", "kind": 2, "importPath": "test_basic", "description": "test_basic", "peekOfCode": "def test_batch_processing():\n    \"\"\"测试批量处理逻辑\"\"\"\n    print(\"\\n4. 测试批量处理逻辑...\")\n    try:\n        # 加载数据\n        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')\n        data = df[['ID', 'Sequence']].head(5)  # 只取前5个样本测试\n        print(f\"   测试数据: {len(data)} 个样本\")\n        # 模拟处理结果\n        results = []", "detail": "test_basic", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_basic", "description": "test_basic", "peekOfCode": "def main():\n    \"\"\"主测试函数\"\"\"\n    print(\"=\"*60)\n    print(\"基础功能测试\")\n    print(\"=\"*60)\n    tests = []\n    # 测试数据加载\n    tests.append(test_data_loading())\n    # 测试模型加载\n    tokenizer, model, device = test_esm_model()", "detail": "test_basic", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "test_basic", "description": "test_basic", "peekOfCode": "logger = logging.getLogger(__name__)\ndef test_data_loading():\n    \"\"\"测试数据加载\"\"\"\n    print(\"1. 测试数据加载...\")\n    try:\n        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')\n        print(f\"   ✓ 成功加载数据，形状: {df.shape}\")\n        print(f\"   列名: {df.columns.tolist()}\")\n        # 检查必需的列\n        if 'ID' in df.columns and 'Sequence' in df.columns:", "detail": "test_basic", "documentation": {}}, {"label": "test_local_esm_model", "kind": 2, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "def test_local_esm_model():\n    \"\"\"测试本地ESM-2模型加载\"\"\"\n    print(\"1. 测试本地ESM-2模型加载...\")\n    model_name = \"facebook/esm2_t33_650M_UR50D\"\n    cache_dir = \"/home/<USER>/.cache/huggingface\"\n    try:\n        print(f\"   模型名称: {model_name}\")\n        print(f\"   缓存目录: {cache_dir}\")\n        # 检查缓存目录\n        model_cache_path = os.path.join(cache_dir, \"hub\", f\"models--facebook--esm2_t33_650M_UR50D\")", "detail": "test_local_models", "documentation": {}}, {"label": "test_simple_feature_extraction", "kind": 2, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "def test_simple_feature_extraction(tokenizer, model, device):\n    \"\"\"测试简单的特征提取\"\"\"\n    print(\"\\n2. 测试简单特征提取...\")\n    if tokenizer is None or model is None:\n        print(\"   ✗ 模型未加载，跳过测试\")\n        return False\n    try:\n        # 使用一个短序列进行测试\n        test_sequence = \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\"\n        print(f\"   测试序列: {test_sequence[:30]}...\")", "detail": "test_local_models", "documentation": {}}, {"label": "test_data_sample", "kind": 2, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "def test_data_sample():\n    \"\"\"测试处理数据样本\"\"\"\n    print(\"\\n3. 测试数据样本处理...\")\n    try:\n        # 加载数据\n        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')\n        print(f\"   ✓ 数据加载成功: {df.shape}\")\n        # 取前3个样本\n        sample_data = df[['ID', 'Sequence']].head(3)\n        print(f\"   测试样本数: {len(sample_data)}\")", "detail": "test_local_models", "documentation": {}}, {"label": "test_memory_usage", "kind": 2, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "def test_memory_usage():\n    \"\"\"测试内存使用情况\"\"\"\n    print(\"\\n4. 测试内存使用...\")\n    try:\n        if torch.cuda.is_available():\n            # GPU内存\n            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3\n            gpu_allocated = torch.cuda.memory_allocated() / 1024**3\n            gpu_cached = torch.cuda.memory_reserved() / 1024**3\n            print(f\"   GPU总内存: {gpu_memory:.2f} GB\")", "detail": "test_local_models", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "def main():\n    \"\"\"主测试函数\"\"\"\n    print(\"=\"*60)\n    print(\"本地模型加载和基础功能测试\")\n    print(\"=\"*60)\n    # 测试本地模型加载\n    tokenizer, model, device = test_local_esm_model()\n    if tokenizer is None or model is None:\n        print(\"\\n✗ 模型加载失败，无法继续测试\")\n        return", "detail": "test_local_models", "documentation": {}}, {"label": "os.environ['TRANSFORMERS_OFFLINE']", "kind": 5, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "os.environ['TRANSFORMERS_OFFLINE'] = '1'\nos.environ['HF_DATASETS_OFFLINE'] = '1'\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\ndef test_local_esm_model():\n    \"\"\"测试本地ESM-2模型加载\"\"\"\n    print(\"1. 测试本地ESM-2模型加载...\")\n    model_name = \"facebook/esm2_t33_650M_UR50D\"\n    cache_dir = \"/home/<USER>/.cache/huggingface\"\n    try:", "detail": "test_local_models", "documentation": {}}, {"label": "os.environ['HF_DATASETS_OFFLINE']", "kind": 5, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "os.environ['HF_DATASETS_OFFLINE'] = '1'\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\ndef test_local_esm_model():\n    \"\"\"测试本地ESM-2模型加载\"\"\"\n    print(\"1. 测试本地ESM-2模型加载...\")\n    model_name = \"facebook/esm2_t33_650M_UR50D\"\n    cache_dir = \"/home/<USER>/.cache/huggingface\"\n    try:\n        print(f\"   模型名称: {model_name}\")", "detail": "test_local_models", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "test_local_models", "description": "test_local_models", "peekOfCode": "logger = logging.getLogger(__name__)\ndef test_local_esm_model():\n    \"\"\"测试本地ESM-2模型加载\"\"\"\n    print(\"1. 测试本地ESM-2模型加载...\")\n    model_name = \"facebook/esm2_t33_650M_UR50D\"\n    cache_dir = \"/home/<USER>/.cache/huggingface\"\n    try:\n        print(f\"   模型名称: {model_name}\")\n        print(f\"   缓存目录: {cache_dir}\")\n        # 检查缓存目录", "detail": "test_local_models", "documentation": {}}, {"label": "get_local_model_path", "kind": 2, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "def get_local_model_path():\n    \"\"\"获取本地模型路径\"\"\"\n    base_path = \"/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D\"\n    # 查找snapshots目录下的实际模型路径\n    snapshots_path = os.path.join(base_path, \"snapshots\")\n    if os.path.exists(snapshots_path):\n        # 获取第一个snapshot目录\n        snapshot_dirs = [d for d in os.listdir(snapshots_path) if os.path.isdir(os.path.join(snapshots_path, d))]\n        if snapshot_dirs:\n            model_path = os.path.join(snapshots_path, snapshot_dirs[0])", "detail": "test_offline", "documentation": {}}, {"label": "test_offline_model_loading", "kind": 2, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "def test_offline_model_loading():\n    \"\"\"测试离线模型加载\"\"\"\n    print(\"1. 测试离线模型加载...\")\n    try:\n        # 获取本地模型路径\n        model_path = get_local_model_path()\n        if not model_path:\n            print(\"   ✗ 未找到本地模型路径\")\n            return None, None, None\n        print(f\"   本地模型路径: {model_path}\")", "detail": "test_offline", "documentation": {}}, {"label": "test_feature_extraction_offline", "kind": 2, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "def test_feature_extraction_offline(tokenizer, model, device):\n    \"\"\"测试离线特征提取\"\"\"\n    print(\"\\n2. 测试离线特征提取...\")\n    if tokenizer is None or model is None:\n        print(\"   ✗ 模型未加载，跳过测试\")\n        return None\n    try:\n        # 测试序列\n        test_sequence = \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\"\n        print(f\"   测试序列长度: {len(test_sequence)}\")", "detail": "test_offline", "documentation": {}}, {"label": "test_batch_simulation", "kind": 2, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "def test_batch_simulation(tokenizer, model, device):\n    \"\"\"测试批量处理模拟\"\"\"\n    print(\"\\n3. 测试批量处理模拟...\")\n    if tokenizer is None or model is None:\n        print(\"   ✗ 模型未加载，跳过测试\")\n        return False\n    try:\n        # 加载数据样本\n        df = pd.read_excel('Km_Data.xlsx', engine='openpyxl')\n        sample_data = df[['ID', 'Sequence']].head(3)", "detail": "test_offline", "documentation": {}}, {"label": "test_memory_cleanup", "kind": 2, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "def test_memory_cleanup():\n    \"\"\"测试内存清理\"\"\"\n    print(\"\\n4. 测试内存清理...\")\n    try:\n        if torch.cuda.is_available():\n            # 显示当前GPU内存使用\n            allocated_before = torch.cuda.memory_allocated() / 1024**3\n            cached_before = torch.cuda.memory_reserved() / 1024**3\n            print(f\"   清理前 - 已分配: {allocated_before:.2f} GB, 缓存: {cached_before:.2f} GB\")\n            # 清理GPU内存", "detail": "test_offline", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "def main():\n    \"\"\"主测试函数\"\"\"\n    print(\"=\"*60)\n    print(\"完全离线模式功能测试\")\n    print(\"=\"*60)\n    # 测试离线模型加载\n    tokenizer, model, device = test_offline_model_loading()\n    if tokenizer is None or model is None:\n        print(\"\\n✗ 离线模型加载失败，无法继续测试\")\n        print(\"请检查模型文件是否完整\")", "detail": "test_offline", "documentation": {}}, {"label": "os.environ['TRANSFORMERS_OFFLINE']", "kind": 5, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "os.environ['TRANSFORMERS_OFFLINE'] = '1'\nos.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\ndef get_local_model_path():\n    \"\"\"获取本地模型路径\"\"\"\n    base_path = \"/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D\"\n    # 查找snapshots目录下的实际模型路径\n    snapshots_path = os.path.join(base_path, \"snapshots\")", "detail": "test_offline", "documentation": {}}, {"label": "os.environ['HF_DATASETS_OFFLINE']", "kind": 5, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "os.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\ndef get_local_model_path():\n    \"\"\"获取本地模型路径\"\"\"\n    base_path = \"/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D\"\n    # 查找snapshots目录下的实际模型路径\n    snapshots_path = os.path.join(base_path, \"snapshots\")\n    if os.path.exists(snapshots_path):", "detail": "test_offline", "documentation": {}}, {"label": "os.environ['HF_HUB_OFFLINE']", "kind": 5, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "os.environ['HF_HUB_OFFLINE'] = '1'\nlogging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(__name__)\ndef get_local_model_path():\n    \"\"\"获取本地模型路径\"\"\"\n    base_path = \"/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D\"\n    # 查找snapshots目录下的实际模型路径\n    snapshots_path = os.path.join(base_path, \"snapshots\")\n    if os.path.exists(snapshots_path):\n        # 获取第一个snapshot目录", "detail": "test_offline", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "test_offline", "description": "test_offline", "peekOfCode": "logger = logging.getLogger(__name__)\ndef get_local_model_path():\n    \"\"\"获取本地模型路径\"\"\"\n    base_path = \"/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D\"\n    # 查找snapshots目录下的实际模型路径\n    snapshots_path = os.path.join(base_path, \"snapshots\")\n    if os.path.exists(snapshots_path):\n        # 获取第一个snapshot目录\n        snapshot_dirs = [d for d in os.listdir(snapshots_path) if os.path.isdir(os.path.join(snapshots_path, d))]\n        if snapshot_dirs:", "detail": "test_offline", "documentation": {}}, {"label": "ESM_MSA_FusionModel", "kind": 6, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "class ESM_MSA_FusionModel(nn.Module):\n    \"\"\"ESM-2 + MSA特征融合模型\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):\n        \"\"\"\n        初始化融合模型\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度  \n            fusion_dim: 融合后特征维度\n        \"\"\"", "detail": "true_fusion_processor", "documentation": {}}, {"label": "TrueFusionProcessor", "kind": 6, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "class TrueFusionProcessor:\n    \"\"\"真正的ESM-2 + MSA融合处理器\"\"\"\n    def __init__(self, \n                 output_dir: str = \"true_fusion_output\",\n                 checkpoint_interval: int = 5,\n                 max_sequence_length: int = 1024):\n        \"\"\"\n        初始化处理器\n        Args:\n            output_dir: 输出目录", "detail": "true_fusion_processor", "documentation": {}}, {"label": "test_fusion_models", "kind": 2, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "def test_fusion_models():\n    \"\"\"测试融合模型加载和基本功能\"\"\"\n    print(\"=\"*60)\n    print(\"测试ESM-2 + MSA融合模型\")\n    print(\"=\"*60)\n    try:\n        # 初始化处理器\n        processor = TrueFusionProcessor(output_dir=\"test_fusion_output\")\n        # 测试序列\n        test_sequence = \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\"", "detail": "true_fusion_processor", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\"*80)\n    print(\"真正的ESM-2 + MSA特征融合处理器\")\n    print(\"使用本地模型，支持最大序列长度\")\n    print(\"=\"*80)\n    # 检查输入文件\n    input_file = \"Km_Data.xlsx\"\n    if not os.path.exists(input_file):\n        print(f\"错误: 找不到输入文件 {input_file}\")", "detail": "true_fusion_processor", "documentation": {}}, {"label": "os.environ['TRANSFORMERS_OFFLINE']", "kind": 5, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "os.environ['TRANSFORMERS_OFFLINE'] = '1'\nos.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('true_fusion_processing.log'),\n        logging.StreamHandler()", "detail": "true_fusion_processor", "documentation": {}}, {"label": "os.environ['HF_DATASETS_OFFLINE']", "kind": 5, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "os.environ['HF_DATASETS_OFFLINE'] = '1'\nos.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('true_fusion_processing.log'),\n        logging.StreamHandler()\n    ]", "detail": "true_fusion_processor", "documentation": {}}, {"label": "os.environ['HF_HUB_OFFLINE']", "kind": 5, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "os.environ['HF_HUB_OFFLINE'] = '1'\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO,\n    format='%(asctime)s - %(levelname)s - %(message)s',\n    handlers=[\n        logging.FileHandler('true_fusion_processing.log'),\n        logging.StreamHandler()\n    ]\n)", "detail": "true_fusion_processor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "true_fusion_processor", "description": "true_fusion_processor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass ESM_MSA_FusionModel(nn.Module):\n    \"\"\"ESM-2 + MSA特征融合模型\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 768, fusion_dim: int = 1280):\n        \"\"\"\n        初始化融合模型\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度  \n            fusion_dim: 融合后特征维度", "detail": "true_fusion_processor", "documentation": {}}, {"label": "visualize_pair_representation", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def visualize_pair_representation(pair_repr: torch.Tensor, sequence: str,\n                                 title: str = \"Pair Representation\", save_path: Optional[str] = None,\n                                 figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化Pair representation\n    Args:\n        pair_repr: Pair表示 (seq_len, seq_len, pair_dim) 或 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题\n        save_path: 保存路径", "detail": "utils", "documentation": {}}, {"label": "visualize_contact_map", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def visualize_contact_map(contact_probs: torch.Tensor, sequence: str,\n                         threshold: float = 0.5, save_path: Optional[str] = None):\n    \"\"\"\n    可视化接触图\n    Args:\n        contact_probs: 接触概率 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        threshold: 接触阈值\n        save_path: 保存路径\n    \"\"\"", "detail": "utils", "documentation": {}}, {"label": "visualize_msa_representation", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def visualize_msa_representation(msa_repr: torch.Tensor, sequence: str,\n                                max_sequences: int = 50, save_path: Optional[str] = None):\n    \"\"\"\n    可视化MSA representation\n    Args:\n        msa_repr: MSA表示 (num_sequences, seq_len, msa_dim)\n        sequence: 蛋白质序列\n        max_sequences: 最大显示序列数\n        save_path: 保存路径\n    \"\"\"", "detail": "utils", "documentation": {}}, {"label": "plot_representation_evolution", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def plot_representation_evolution(evolution_data: List[torch.Tensor], \n                                 representation_type: str = \"msa\",\n                                 save_path: Optional[str] = None):\n    \"\"\"\n    绘制表示在不同层的演化\n    Args:\n        evolution_data: 不同层的表示列表\n        representation_type: 表示类型 (\"msa\", \"pair\", \"single\")\n        save_path: 保存路径\n    \"\"\"", "detail": "utils", "documentation": {}}, {"label": "analyze_evoformer_outputs", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def analyze_evoformer_outputs(results: Dict[str, torch.Tensor]) -> Dict[str, float]:\n    \"\"\"\n    分析Evoformer输出\n    Args:\n        results: 模型输出结果\n    Returns:\n        分析结果\n    \"\"\"\n    analysis = {}\n    # 分析MSA表示", "detail": "utils", "documentation": {}}, {"label": "save_evoformer_results", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def save_evoformer_results(results: Dict[str, torch.Tensor], save_path: str):\n    \"\"\"\n    保存Evoformer结果\n    Args:\n        results: 模型输出结果\n        save_path: 保存路径\n    \"\"\"\n    # 转换为numpy格式保存\n    results_np = {}\n    for key, value in results.items():", "detail": "utils", "documentation": {}}, {"label": "load_evoformer_results", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def load_evoformer_results(load_path: str) -> Dict:\n    \"\"\"\n    加载Evoformer结果\n    Args:\n        load_path: 文件路径\n    Returns:\n        结果字典\n    \"\"\"\n    data = np.load(load_path, allow_pickle=True)\n    results = {}", "detail": "utils", "documentation": {}}, {"label": "print_evoformer_summary", "kind": 2, "importPath": "utils", "description": "utils", "peekOfCode": "def print_evoformer_summary(results: Dict[str, torch.Tensor]):\n    \"\"\"\n    打印Evoformer分析摘要\n    Args:\n        results: 模型输出结果\n    \"\"\"\n    print(\"=\" * 60)\n    print(\"EVOFORMER ARCHITECTURAL FUSION SUMMARY\")\n    print(\"=\" * 60)\n    if \"sequence\" in results:", "detail": "utils", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "utils", "description": "utils", "peekOfCode": "logger = logging.getLogger(__name__)\ndef visualize_pair_representation(pair_repr: torch.Tensor, sequence: str,\n                                 title: str = \"Pair Representation\", save_path: Optional[str] = None,\n                                 figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化Pair representation\n    Args:\n        pair_repr: Pair表示 (seq_len, seq_len, pair_dim) 或 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题", "detail": "utils", "documentation": {}}]