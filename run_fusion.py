#!/usr/bin/env python3
"""
蛋白质架构级融合特征提取运行脚本
简化版本，直接处理Km_Data.xlsx文件
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from architectural_fusion_processor import ArchitecturalFusionProcessor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    print("="*80)
    print("蛋白质架构级融合特征提取器")
    print("ESM-2 + MSA Transformer 融合")
    print("="*80)
    
    # 检查输入文件
    input_file = "Km_Data.xlsx"
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        print("请确保 Km_Data.xlsx 文件在当前目录中")
        return
    
    print(f"输入文件: {input_file}")
    
    # 创建输出目录
    output_dir = "fusion_output"
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 初始化处理器
    print("\n初始化处理器...")
    try:
        processor = ArchitecturalFusionProcessor(
            esm_model_name="facebook/esm2_t33_650M_UR50D",
            batch_size=1,
            output_dir=output_dir,
            checkpoint_interval=5  # 每5个样本保存一次
        )
        print("[OK] 处理器初始化成功")
    except Exception as e:
        print(f"[FAIL] 处理器初始化失败: {e}")
        return
    
    try:
        # 加载数据
        print(f"\n加载数据...")
        data = processor.load_data(input_file)
        
        print(f"数据概览:")
        print(f"  总样本数: {len(data)}")
        
        # 序列长度统计
        seq_lengths = data['Sequence'].str.len()
        print(f"  序列长度统计:")
        print(f"    最小: {seq_lengths.min()}")
        print(f"    最大: {seq_lengths.max()}")
        print(f"    平均: {seq_lengths.mean():.1f}")
        print(f"    中位数: {seq_lengths.median():.1f}")
        
        # 检查超长序列
        long_seqs = seq_lengths > 1000
        if long_seqs.any():
            print(f"    超长序列 (>1000): {long_seqs.sum()} 个 (将被跳过)")
        
        # 显示前几个样本
        print(f"\n前3个样本:")
        for i, row in data.head(3).iterrows():
            print(f"  {i+1}. ID: {row['ID']}, 长度: {len(row['Sequence'])}")
        
        # 询问是否继续
        print(f"\n准备处理 {len(data)} 个蛋白质样本")
        print("这可能需要较长时间，特别是对于长序列...")
        
        response = input("是否继续? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("处理已取消")
            return
        
        # 开始处理
        print(f"\n开始处理...")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("注意: 可以随时按 Ctrl+C 中断，进度会自动保存")
        
        # 批量处理
        results = processor.process_batch(data)
        
        # 显示结果
        print(f"\n处理完成!")
        
        # 统计信息
        stats = processor.get_processing_stats()
        print(f"\n统计信息:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")
        
        # 输出文件信息
        output_file = os.path.join(output_dir, "architectural_fusion_vectors.csv")
        if os.path.exists(output_file):
            print(f"\n输出文件: {output_file}")
            
            # 读取并显示结果概览
            try:
                result_df = pd.read_csv(output_file)
                print(f"输出文件包含 {len(result_df)} 条记录")
                
                # 成功处理的样本
                successful = result_df['fusion_vector'].notna()
                print(f"成功处理: {successful.sum()} 个")
                print(f"处理失败: {(~successful).sum()} 个")
                
                if successful.any():
                    # 分析特征维度
                    first_successful = result_df[successful].iloc[0]
                    vector_str = first_successful['fusion_vector']
                    if isinstance(vector_str, str):
                        vector_dim = len(vector_str.split(','))
                        print(f"特征向量维度: {vector_dim}")
                
            except Exception as e:
                print(f"读取输出文件时出错: {e}")
        
        print(f"\n处理完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
    except KeyboardInterrupt:
        print(f"\n\n处理被用户中断")
        print("进度已保存，可以稍后继续处理")
        print("要继续处理，请重新运行此脚本")
        
    except Exception as e:
        print(f"\n处理过程中出现错误: {e}")
        logger.error(f"处理错误: {e}", exc_info=True)


def show_progress():
    """显示当前处理进度"""
    output_dir = "fusion_output"
    processor = ArchitecturalFusionProcessor(output_dir=output_dir)
    
    try:
        stats = processor.get_processing_stats()
        print("当前处理进度:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")
    except Exception as e:
        print(f"获取进度信息失败: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--progress":
        show_progress()
    else:
        main()
